# Multi-Port Serial Port API - Usage Guide

## Overview

The serial port library has been upgraded to support multiple COM port connections simultaneously while maintaining full backward compatibility with existing code. You can now connect to multiple serial devices at once, each with their own configuration and data streams.

## Key Features

- ✅ **Multiple simultaneous connections** - Connect to multiple COM ports at once
- ✅ **Independent operation** - Each port operates independently with its own error handling and reconnection logic
- ✅ **Backward compatibility** - All existing code continues to work without changes
- ✅ **Flexible port identification** - Use meaningful names for your ports (e.g., 'player1', 'sensors', 'arduino')
- ✅ **Continuous listening** - Each port maintains continuous listening as preferred
- ✅ **Per-port configuration** - Each port can have different baud rates and settings

## New Multi-Port API

### Connecting to Multiple Ports

```typescript
import { 
  connectToPort, 
  writeToPort, 
  getPortDataById, 
  getPortStateById,
  disconnectPort,
  listConnectedPorts 
} from '$lib/serial-port';

// Connect to multiple ports with different configurations
await connectToPort('player1', { baudRate: 9600 });
await connectToPort('player2', { baudRate: 115200 });
await connectToPort('sensors', { baudRate: 57600 });

// Check which ports are connected
const connectedPorts = listConnectedPorts();
console.log('Connected ports:', connectedPorts); // ['player1', 'player2', 'sensors']
```

### Writing to Specific Ports

```typescript
// Write different commands to different ports
await writeToPort('player1', 'LED_ON');
await writeToPort('player2', 'BUZZER_BEEP');
await writeToPort('sensors', 'READ_TEMP');
```

### Reading Data from Specific Ports

```typescript
// Get data from specific ports
const player1Data = getPortDataById('player1');
const player2Data = getPortDataById('player2');
const sensorData = getPortDataById('sensors');

console.log('Player 1:', player1Data); // ['0', '1', '0', '0', '0', '0']
console.log('Player 2:', player2Data); // ['1', '0', '1', '0', '0', '0']
console.log('Sensors:', sensorData);   // ['2', '1', '0', '1', '0', '0']
```

### Monitoring Port States

```typescript
// Check individual port states
const player1State = getPortStateById('player1');
const player2State = getPortStateById('player2');

if (player1State?.isConnected) {
  console.log('Player 1 is connected');
}

if (player2State?.error) {
  console.error('Player 2 error:', player2State.error);
}
```

### Using Reactive Stores

```typescript
import { portDataMap, portErrorMap, multiPortState } from '$lib/serial-port';

// Subscribe to all port data changes
portDataMap.subscribe(dataMap => {
  for (const [portId, data] of dataMap) {
    console.log(`Port ${portId} data:`, data);
  }
});

// Subscribe to port errors
portErrorMap.subscribe(errorMap => {
  for (const [portId, error] of errorMap) {
    if (error) {
      console.error(`Port ${portId} error:`, error.message);
    }
  }
});

// Subscribe to overall multi-port state
multiPortState.subscribe(state => {
  console.log('Total connections:', state.connections.size);
  console.log('Default port:', state.defaultPortId);
});
```

### Advanced Port Management

```typescript
// Set a default port for legacy API calls
setDefaultPort('player1');

// Force reconnect a specific port
await forceReconnectPort('player2');

// Disconnect specific ports
await disconnectPort('sensors');

// Disconnect all ports
await disconnectAllPorts();

// Check if a specific port is ready
if (isPortReadyById('player1')) {
  await writeToPort('player1', 'READY_COMMAND');
}
```

## Backward Compatibility

All existing code continues to work exactly as before. The legacy API now uses a "default" port internally:

```typescript
// This still works exactly as before
import { 
  connectToSerialPort, 
  writeToSerialPort, 
  serialPortData, 
  serialPortError,
  portState 
} from '$lib/serial-port';

// Connects to the 'default' port
await connectToSerialPort({ baudRate: 9600 });

// Writes to the default port
await writeToSerialPort('Hello Arduino!');

// Legacy stores still work
serialPortData.subscribe(data => {
  console.log('Default port data:', data);
});
```

## Migration Examples

### Before (Single Port)
```typescript
// Old way - single port only
await connectToSerialPort();
await writeToSerialPort('command');
serialPortData.subscribe(data => console.log(data));
```

### After (Multi-Port)
```typescript
// New way - multiple ports
await connectToPort('device1', { baudRate: 9600 });
await connectToPort('device2', { baudRate: 115200 });

await writeToPort('device1', 'command1');
await writeToPort('device2', 'command2');

portDataMap.subscribe(dataMap => {
  const device1Data = dataMap.get('device1');
  const device2Data = dataMap.get('device2');
  console.log('Device 1:', device1Data);
  console.log('Device 2:', device2Data);
});

// Or keep using legacy API for one device
await connectToSerialPort(); // Uses 'default' port
serialPortData.subscribe(data => console.log('Default:', data));
```

## Error Handling

Each port has independent error handling:

```typescript
// Monitor errors for specific ports
portErrorMap.subscribe(errorMap => {
  const player1Error = errorMap.get('player1');
  const player2Error = errorMap.get('player2');
  
  if (player1Error) {
    console.error('Player 1 issue:', player1Error.message);
    // Handle player 1 specific error
  }
  
  if (player2Error) {
    console.error('Player 2 issue:', player2Error.message);
    // Handle player 2 specific error
  }
});
```

## Best Practices

1. **Use meaningful port IDs**: Instead of 'port1', 'port2', use 'player1', 'sensors', 'arduino', etc.
2. **Check connection status**: Always verify ports are connected before writing
3. **Handle errors gracefully**: Each port can fail independently
4. **Set appropriate configurations**: Different devices may need different baud rates
5. **Clean up connections**: Disconnect ports when no longer needed

## Complete Example

```typescript
import { 
  connectToPort, 
  writeToPort, 
  portDataMap, 
  portErrorMap,
  disconnectAllPorts 
} from '$lib/serial-port';

class MultiDeviceController {
  async initialize() {
    // Connect to multiple devices
    await connectToPort('player1', { baudRate: 9600 });
    await connectToPort('player2', { baudRate: 9600 });
    await connectToPort('sensors', { baudRate: 115200 });
    
    // Set up data monitoring
    this.setupDataMonitoring();
    this.setupErrorHandling();
  }
  
  setupDataMonitoring() {
    portDataMap.subscribe(dataMap => {
      const player1Data = dataMap.get('player1');
      const player2Data = dataMap.get('player2');
      const sensorData = dataMap.get('sensors');
      
      if (player1Data) this.handlePlayer1Data(player1Data);
      if (player2Data) this.handlePlayer2Data(player2Data);
      if (sensorData) this.handleSensorData(sensorData);
    });
  }
  
  setupErrorHandling() {
    portErrorMap.subscribe(errorMap => {
      for (const [portId, error] of errorMap) {
        if (error) {
          console.error(`Port ${portId} error:`, error.message);
          this.handlePortError(portId, error);
        }
      }
    });
  }
  
  async sendCommands() {
    await writeToPort('player1', 'START_GAME');
    await writeToPort('player2', 'START_GAME');
    await writeToPort('sensors', 'BEGIN_MONITORING');
  }
  
  async cleanup() {
    await disconnectAllPorts();
  }
  
  handlePlayer1Data(data) { /* Handle player 1 input */ }
  handlePlayer2Data(data) { /* Handle player 2 input */ }
  handleSensorData(data) { /* Handle sensor readings */ }
  handlePortError(portId, error) { /* Handle port-specific errors */ }
}
```

This upgrade provides powerful multi-port capabilities while ensuring your existing code continues to work seamlessly!
