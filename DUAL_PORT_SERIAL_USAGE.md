# Dual-Port Serial System - Auto-Connect Usage Guide

## Overview

The serial port library has been simplified to support exactly 2 COM ports with smart auto-connect functionality:
- **🎮 Buttons Port** - For reading button inputs
- **💡 LEDs Port** - For controlling LEDs

## Key Features

- ✅ **Auto-connect** - Automatically connects using saved configuration
- ✅ **One-time setup** - Configuration popup only shows once or when requested
- ✅ **Smart connection** - Auto-connect first, fallback to configuration if needed
- ✅ **Two dedicated ports** - One for buttons, one for LEDs
- ✅ **Backward compatibility** - Existing code still works
- ✅ **Continuous listening** - Always listening to both ports
- ✅ **Independent operation** - Each port works independently

## New Auto-Connect API

### Smart Connection (Recommended)

```typescript
import {
  connectToBothPorts,
  buttonsPortData,
  ledsPortData,
  writeToButtons,
  writeToLeds,
  disconnectBothPorts
} from '$lib/serial-port';

// Smart connect: auto-connect if configured, show popup only if needed
const result = await connectToBothPorts();
console.log('Buttons connected:', result.buttons);
console.log('LEDs connected:', result.leds);
```

### Manual Control Options

```typescript
import {
  autoConnect,
  configureAndConnect,
  isConfigured,
  clearConfiguration
} from '$lib/serial-port';

// Check if already configured
if (isConfigured()) {
  console.log('Ports are already configured');

  // Auto-connect without popup
  const result = await autoConnect();
  if (!result.buttons || !result.leds) {
    console.log('Auto-connect failed, may need reconfiguration');
  }
} else {
  console.log('First time setup needed');
}

// Force show configuration dialog (for settings/reconfiguration)
const result = await configureAndConnect();

// Clear saved configuration (for reset)
clearConfiguration();
```

### Reading Data from Each Port

```typescript
// Subscribe to buttons data
buttonsPortData.subscribe(data => {
  console.log('Button data:', data); // ['0', '1', '0', '0', '0', '0']
  
  // Handle button presses
  const [btn1, btn2, btn3, btn4, btn5, btn6] = data;
  if (btn1 === '1') console.log('Button 1 pressed!');
  if (btn2 === '1') console.log('Button 2 pressed!');
});

// Subscribe to LEDs data (if LEDs send feedback)
ledsPortData.subscribe(data => {
  console.log('LED feedback:', data);
});
```

### Writing to Each Port

```typescript
// Send commands to buttons port
await writeToButtons('RESET_BUTTONS');

// Send commands to LEDs port
await writeToLeds('LED_ON_1');
await writeToLeds('LED_OFF_2');
await writeToLeds('BLINK_ALL');
```

### Connection Status

```typescript
import { 
  isButtonsConnected,
  isLedsConnected,
  areBothConnected,
  dualPortState
} from '$lib/serial-port';

// Check individual connections
if (isButtonsConnected()) {
  console.log('Buttons port is ready');
}

if (isLedsConnected()) {
  console.log('LEDs port is ready');
}

// Check both connections
if (areBothConnected()) {
  console.log('Both ports are connected and ready');
}

// Subscribe to overall state
dualPortState.subscribe(state => {
  console.log('Buttons connected:', state.buttonsConnected);
  console.log('LEDs connected:', state.ledsConnected);
  console.log('Current button data:', state.buttonsData);
  console.log('Current LED data:', state.ledsData);
});
```

### Error Handling

```typescript
import { buttonsPortError, ledsPortError } from '$lib/serial-port';

// Monitor buttons port errors
buttonsPortError.subscribe(error => {
  if (error) {
    console.error('Buttons port error:', error.message);
    // Handle buttons port error
  }
});

// Monitor LEDs port errors
ledsPortError.subscribe(error => {
  if (error) {
    console.error('LEDs port error:', error.message);
    // Handle LEDs port error
  }
});
```

## Port Selection Dialog

When you call `connectToBothPorts()`, a popup will appear showing:

```
┌─────────────────────────────────────┐
│           Select COM Ports          │
├─────────────────────────────────────┤
│ Please assign the COM ports to      │
│ their respective functions:         │
│                                     │
│ 🎮 Buttons Port:                    │
│ [Port 1 (VID: 1234, PID: 5678) ▼] │
│                                     │
│ 💡 LEDs Port:                       │
│ [Port 2 (VID: 9ABC, PID: DEF0) ▼] │
│                                     │
│              [Cancel] [Connect]     │
└─────────────────────────────────────┘
```

- The dialog automatically selects different ports by default
- You can reassign ports using the dropdowns
- Click "Connect" to establish connections
- Press Escape or click "Cancel" to abort

## Complete Example with Auto-Connect

```typescript
import {
  connectToBothPorts,
  autoConnect,
  configureAndConnect,
  isConfigured,
  buttonsPortData,
  ledsPortData,
  writeToButtons,
  writeToLeds,
  buttonsPortError,
  ledsPortError,
  disconnectBothPorts
} from '$lib/serial-port';

class GameController {
  async initialize() {
    // Smart connect: auto-connect if configured, show popup only if needed
    const result = await connectToBothPorts();
    
    if (!result.buttons) {
      console.error('Failed to connect to buttons port');
      return false;
    }
    
    if (!result.leds) {
      console.error('Failed to connect to LEDs port');
      return false;
    }
    
    // Set up data monitoring
    this.setupButtonMonitoring();
    this.setupLEDMonitoring();
    this.setupErrorHandling();
    
    // Initialize devices
    await writeToButtons('INIT');
    await writeToLeds('INIT_LEDS');
    
    console.log('Game controller initialized successfully');
    return true;
  }
  
  setupButtonMonitoring() {
    buttonsPortData.subscribe(data => {
      const [btn1, btn2, btn3, btn4, btn5, btn6] = data;
      
      // Handle player 1 buttons (first 3)
      if (btn1 === '1') this.handlePlayer1Button(1);
      if (btn2 === '1') this.handlePlayer1Button(2);
      if (btn3 === '1') this.handlePlayer1Button(3);
      
      // Handle player 2 buttons (last 3)
      if (btn4 === '1') this.handlePlayer2Button(1);
      if (btn5 === '1') this.handlePlayer2Button(2);
      if (btn6 === '1') this.handlePlayer2Button(3);
    });
  }
  
  setupLEDMonitoring() {
    ledsPortData.subscribe(data => {
      // Handle LED feedback if your LEDs send status back
      console.log('LED status:', data);
    });
  }
  
  setupErrorHandling() {
    buttonsPortError.subscribe(error => {
      if (error) {
        console.error('Buttons error:', error.message);
        this.handleButtonsError(error);
      }
    });
    
    ledsPortError.subscribe(error => {
      if (error) {
        console.error('LEDs error:', error.message);
        this.handleLEDsError(error);
      }
    });
  }
  
  handlePlayer1Button(buttonNumber: number) {
    console.log(`Player 1 pressed button ${buttonNumber}`);
    // Light up corresponding LED
    writeToLeds(`LED_ON_P1_${buttonNumber}`);
  }
  
  handlePlayer2Button(buttonNumber: number) {
    console.log(`Player 2 pressed button ${buttonNumber}`);
    // Light up corresponding LED
    writeToLeds(`LED_ON_P2_${buttonNumber}`);
  }
  
  async startGame() {
    // Send start signals
    await writeToButtons('GAME_START');
    await writeToLeds('START_ANIMATION');
  }
  
  async endGame() {
    // Send end signals
    await writeToButtons('GAME_END');
    await writeToLeds('END_ANIMATION');
  }
  
  async cleanup() {
    await disconnectBothPorts();
    console.log('Game controller cleaned up');
  }

  handleButtonsError(error: any) {
    // Handle buttons port errors
    console.log('Attempting to reconnect buttons...');
  }

  handleLEDsError(error: any) {
    // Handle LEDs port errors
    console.log('Attempting to reconnect LEDs...');
  }

  // Method to reconfigure ports (for settings menu)
  async reconfigurePorts() {
    console.log('Showing port configuration dialog...');
    const result = await configureAndConnect();
    if (result.buttons && result.leds) {
      console.log('Ports reconfigured successfully');
    }
  }
}

// Usage
const gameController = new GameController();
await gameController.initialize();

// Later, if user wants to reconfigure ports:
// await gameController.reconfigurePorts();
```

## App Startup Flow

```typescript
// In your app initialization (e.g., onMount in Svelte)
import { connectToBothPorts, isConfigured } from '$lib/serial-port';

async function initializeApp() {
  console.log('Initializing app...');

  // This will:
  // 1. Check if ports are already configured
  // 2. If yes: auto-connect silently
  // 3. If no: show configuration dialog once
  // 4. Save configuration for future auto-connects
  const result = await connectToBothPorts();

  if (result.buttons && result.leds) {
    console.log('Both ports connected successfully');
    // App is ready to use
  } else {
    console.log('Failed to connect to ports');
    // Handle connection failure
  }
}

// Call this once when your app starts
initializeApp();
```

## Settings/Configuration Menu

```typescript
// In your settings component
import { configureAndConnect, clearConfiguration, isConfigured } from '$lib/serial-port';

// Show current configuration status
if (isConfigured()) {
  console.log('Ports are configured');
} else {
  console.log('Ports need configuration');
}

// Button to reconfigure ports
async function reconfigurePorts() {
  const result = await configureAndConnect();
  if (result.buttons && result.leds) {
    alert('Ports reconfigured successfully!');
  } else {
    alert('Failed to configure ports');
  }
}

// Button to reset configuration
function resetConfiguration() {
  clearConfiguration();
  alert('Port configuration cleared. App will show setup dialog on next connection.');
}
```

## Backward Compatibility

Your existing code continues to work unchanged:

```typescript
// This still works - uses buttons port as default
import { 
  connectToSerialPort,
  writeToSerialPort,
  serialPortData
} from '$lib/serial-port';

await connectToSerialPort();
await writeToSerialPort('command');
serialPortData.subscribe(data => console.log(data));
```

## Migration from Single Port

### Before (Single Port)
```typescript
await connectToSerialPort();
serialPortData.subscribe(data => {
  // Handle all data from one port
  console.log('Data:', data);
});
await writeToSerialPort('command');
```

### After (Dual Port)
```typescript
await connectToBothPorts();

// Separate data streams
buttonsPortData.subscribe(data => {
  console.log('Button data:', data);
});

ledsPortData.subscribe(data => {
  console.log('LED data:', data);
});

// Separate write functions
await writeToButtons('button_command');
await writeToLeds('led_command');
```

This simplified dual-port system makes it easy to work with exactly two COM ports while maintaining all the reliability and continuous listening features you need!
