<script lang="ts">
	import '../app.css';

	import { page } from '$app/state';
	import { handleArduinoResponse, handleKeydown, handleKeyup } from '$lib/button.state';
	import { listen, onIdle } from '$lib/idle';
	import { buttonsPortData, connectToBothPorts, writeToButtons } from '$lib/serial-port';
	import { onMount } from 'svelte';

	let { children } = $props();

	listen({ timer: 60000 });

	onIdle(() => {
		if (page.url.pathname !== '/') {
			window.location.href = '/';
		}
	});

	onMount(() => {
		// Listen for keydown events globally
		window.addEventListener('keydown', handleKeydown);
		window.addEventListener('keyup', handleKeyup);

		connectToBothPorts();

		buttonsPortData.subscribe((data) => {
			console.log('Received buttons data:', data);
			handleArduinoResponse(data);
		});

		// Clean up the event listener on component unmount
		return () => {
			window.removeEventListener('keydown', handleKeydown);
			window.removeEventListener('keyup', handleKeyup);
		};
	});
</script>

<section class="relative h-[1080px] w-[1920px] overflow-hidden">
	<button class="absolute top-0 left-0 z-50 size-10 opacity-0" onclick={() => connectToBothPorts()}>
		a
	</button>

	<button class="absolute top-0 right-0 z-50 size-10 opacity-0" onclick={() => writeToButtons('o')}>
		b
	</button>

	<video
		class="absolute top-0 left-0 z-10 size-full"
		src="/assets/bg/primary.mp4"
		autoplay
		loop
		muted
	></video>
	<div class="absolute top-0 left-0 z-20 size-full">
		{@render children()}
	</div>
</section>
