<script lang="ts">
	import { beforeNavigate, goto } from '$app/navigation';
	import { logEvent } from '$lib/api';
	import { appStore } from '$lib/app.state';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { player1, player2 } from '$lib/player.state';
	import { writeToSerialPort } from '$lib/serial-port';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';

	let now = Date.now();
	let showCTA = $state(false);
	let showCountdown = $state(false);
	let counterTimeout = $state<NodeJS.Timeout>();

	let playerUnsubscribe = $state<() => void>(() => {});

	function startCountdown() {
		counterTimeout = setTimeout(() => {
			clearTimeout(counterTimeout);
			goto(`/${$appStore.gameMode}`);
		}, 4000);
	}

	$effect(() => {
		if (showCountdown) {
			startCountdown();
		}
	});

	function subcribePlayers() {
		const unsubPlayer1 = player1.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1 && !showCountdown) {
				showCountdown = true;
			}
		});

		const unsubPlayer2 = player2.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1 && !showCountdown) {
				showCountdown = true;
			}
		});

		return () => {
			unsubPlayer1();
			unsubPlayer2();
		};
	}

	onMount(() => {
		const timeout = setTimeout(() => {
			showCTA = true;
			playerUnsubscribe = subcribePlayers();
		}, 1000);

		writeToSerialPort('o');

		return () => {
			playerUnsubscribe();
			clearTimeout(timeout);
		};
	});

	beforeNavigate(() => {
		logEvent(
			$appStore.interactionId,
			`Player stayed on how to play page for ${Date.now() - now}ms`
		);
	});
</script>

<section
	class="flex size-full flex-col items-center justify-center gap-y-16 text-white"
	transition:fade
>
	{#if !showCountdown}
		<h1 class="font-parkin text-[70px] font-extrabold uppercase">HOW TO PLAY</h1>

		<SimplePngSequencePlayer
			class="absolute top-0 left-0"
			imageUrls={Array.from(
				{ length: 480 },
				(_, i) => `/assets/pngSequences/how-to-play/${String(i).padStart(5, '0')}.png`
			)}
			fps={60}
			loop={true}
		/>
		<div class="h-140 w-425"></div>

		<div class="h-31">
			{#if showCTA}
				<div class="breathing flex items-center gap-x-4" in:fade>
					<img src="/assets/el/red-button.png" alt="red-button" />
					<p class="font-marqona text-[40px] uppercase">Press any button to START</p>
				</div>
			{/if}
		</div>
	{:else}
		<SimplePngSequencePlayer
			class="absolute top-0 left-0"
			imageUrls={Array.from(
				{ length: 300 },
				(_, i) => `/assets/pngSequences/count-down/${String(i).padStart(5, '0')}.png`
			)}
			fps={50}
			loop={false}
		/>
	{/if}
</section>
