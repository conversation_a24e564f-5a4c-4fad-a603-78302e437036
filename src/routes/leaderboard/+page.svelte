<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import { appStore, resetGame } from '$lib/app.state';
	import { player1, player2, resetPlayers } from '$lib/player.state';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { Tween } from 'svelte/motion';
	import { cubicOut } from 'svelte/easing';
	import { endInteraction, getLeaderboard, logEvent, type Leaderboard } from '$lib/api';
	import { writeToSerialPort } from '$lib/serial-port';

	let unsubscribe = $state(() => {});
	let showCountdown = $state(false);
	let countdownInterval = $state<NodeJS.Timeout | undefined>();
	let count = $state(15);
	const INITIAL_COUNT = 15; // Max value for count
	const STROKE_WIDTH = 4; // Corresponds to border-4

	let top2NameElement = $state<HTMLSpanElement | null>(null);
	let top3NameElement = $state<HTMLSpanElement | null>(null);

	let imgElement = $state<HTMLImageElement | null>(null);
	let svgSize = $state({ width: 0, height: 0 });

	let leaderboard = $state<Leaderboard[]>([]);

	$effect(() => {
		const currentImg = imgElement;
		if (currentImg) {
			const updateSize = () => {
				if (currentImg.offsetWidth > 0 && currentImg.offsetHeight > 0) {
					if (
						svgSize.width !== currentImg.offsetWidth ||
						svgSize.height !== currentImg.offsetHeight
					) {
						svgSize = { width: currentImg.offsetWidth, height: currentImg.offsetHeight };
					}
				}
			};

			if (currentImg.complete && currentImg.naturalWidth > 0) {
				updateSize();
			}
			currentImg.addEventListener('load', updateSize);

			const resizeObserver = new ResizeObserver(updateSize);
			resizeObserver.observe(currentImg);

			return () => {
				currentImg.removeEventListener('load', updateSize);
				resizeObserver.unobserve(currentImg);
			};
		} else {
			if (svgSize.width !== 0 || svgSize.height !== 0) {
				svgSize = { width: 0, height: 0 };
			}
		}
	});

	const circleRadius = $derived(
		svgSize.width > 0 && svgSize.height > 0
			? Math.min(svgSize.width, svgSize.height) / 2 - STROKE_WIDTH / 2
			: 0
	);

	const circumference = $derived(2 * Math.PI * circleRadius);
	const progressPercentage = $derived(Math.max(0, count) / INITIAL_COUNT);
	const targetStrokeDashoffset = $derived(circumference * (1 - progressPercentage));

	const animatedStrokeDashoffset = new Tween(circumference, {
		duration: 1000,
		easing: cubicOut
	});

	$effect(() => {
		if (circumference > 0) {
			if (
				animatedStrokeDashoffset.current === undefined ||
				animatedStrokeDashoffset.current > circumference
			) {
				animatedStrokeDashoffset.set(targetStrokeDashoffset, { duration: 0 });
			} else {
				animatedStrokeDashoffset.set(targetStrokeDashoffset);
			}
		} else if (animatedStrokeDashoffset.current !== 0) {
			animatedStrokeDashoffset.set(0, { duration: 0 });
		}
	});

	function subcribePlayers() {
		writeToSerialPort('o');

		const unsubPlayer1 = player1.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1) {
				clearInterval(countdownInterval);
				endInteraction($appStore.interactionId);
				writeToSerialPort('p');
				goto('/');
			}
		});

		const unsubPlayer2 = player2.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1) {
				clearInterval(countdownInterval);
				endInteraction($appStore.interactionId);
				writeToSerialPort('p');
				goto('/');
			}
		});

		return () => {
			unsubPlayer1();
			unsubPlayer2();
		};
	}

	function startCountdown() {
		countdownInterval = setInterval(() => {
			count -= 1;
			if (count <= 0) {
				clearInterval(countdownInterval);
				endInteraction($appStore.interactionId);
				resetPlayers();
				resetGame();
				writeToSerialPort('p');
				goto('/');
			}
		}, 1000);
	}

	onMount(async () => {
		leaderboard = await getLeaderboard();

		if (leaderboard.length < 9) {
			for (let i = leaderboard.length; i < 9; i++) {
				leaderboard.push({
					id: crypto.randomUUID(),
					userId: '',
					score: 0,
					name: '',
					createdAt: ''
				});
			}
		}
	});

	onMount(() => {
		const timeout = setTimeout(() => {
			showCountdown = true;
			startCountdown();
			unsubscribe = subcribePlayers();
		}, 1000);

		logEvent($appStore.interactionId, `Game ended, leaderboard is shown`);

		return () => {
			unsubscribe();
			clearTimeout(timeout);
		};
	});
</script>

<div
	class="flex h-screen w-full flex-col items-center justify-center pt-20 text-white"
	transition:fade
>
	<div class="absolute top-20 right-20">
		<img src="/assets/el/countdown-circle.png" alt="countdown" bind:this={imgElement} />
		{#if svgSize.width > 0 && svgSize.height > 0 && circumference > 0}
			<svg
				width={svgSize.width}
				height={svgSize.height}
				class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
			>
				<circle
					cx={svgSize.width / 2}
					cy={svgSize.height / 2}
					r={circleRadius}
					stroke="white"
					stroke-width={STROKE_WIDTH}
					fill="transparent"
					stroke-dasharray={circumference}
					stroke-dashoffset={animatedStrokeDashoffset.current}
					style="transform: rotate(-90deg); transform-origin: center center;"
				/>
			</svg>
		{/if}
		<span
			class="font-marqona absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-[48px] tracking-widest uppercase"
		>
			{count}<span class="text-[26px]">s</span>
		</span>
	</div>

	<p class="font-parkin text-[60px] font-extrabold tracking-widest uppercase">LEADERBOARD</p>

	<img src="/assets/el/star.png" alt="star" class="absolute top-40 left-[30%] z-10 animate-spin" />
	<img src="/assets/el/star.png" alt="star" class="absolute top-40 right-[30%] z-10 animate-spin" />

	<div class="relative pt-20 pb-10">
		<img src="/assets/el/leaderboard-top3.png" alt="leaderboard" />

		<!-- top 1 -->
		<div class="absolute top-5 left-1/2 flex -translate-x-1/2 items-center justify-center gap-2">
			<div class="relative">
				<img width="50" src="/assets/el/profile-icon-yellow.png" alt="profile" />
				<img
					src="/assets/el/crown.png"
					alt="crown"
					class="absolute -top-3.5 left-1/2 -translate-x-1/2"
				/>
				<span
					class="font-parkin absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pt-1.5 text-center text-[30px] font-bold uppercase"
				>
					{leaderboard[0]?.name[0]}
				</span>
			</div>

			<span class="font-parkin mt-1.5 text-[36px] leading-0 font-bold tracking-widest uppercase">
				{leaderboard[0]?.name || '--------'}
			</span>

			<span
				class="font-parkin absolute top-56 left-1/2 -translate-x-1/2 -translate-y-1/2 pt-1.5 text-center text-[74px] font-bold uppercase"
			>
				{leaderboard[0]?.score}
			</span>
		</div>

		<!-- top 2 -->
		<div class="absolute top-35 left-37 h-10 w-1">
			<div class="absolute top-0 right-12 h-full w-px">
				<div
					class={cn(
						'absolute -top-5 left-1/2 flex h-full w-fit -translate-x-1/2 flex-col items-center justify-center gap-3',
						{
							'right-4': top2NameElement?.clientWidth! < 70,
							'right-7': top2NameElement?.clientWidth! < 50
						}
					)}
				>
					<div class="relative h-10 min-w-10">
						<img width="38" src="/assets/el/profile-icon-yellow.png" alt="profile" />
						<span
							class="font-parkin absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pt-1 text-center text-[22px] font-bold uppercase"
						>
							{leaderboard[1]?.name[0]}
						</span>
					</div>

					<span
						bind:this={top2NameElement}
						class="font-parkin mt-2 text-[18px] leading-0 font-bold tracking-widest text-nowrap uppercase"
					>
						{leaderboard[1]?.name || '--------'}
					</span>
				</div>
			</div>
			<div class="absolute top-29 right-12 w-px">
				<span
					class="font-parkin absolute left-1/2 -translate-x-1/2 pt-1.5 text-center text-[55.55px] font-bold uppercase"
				>
					{leaderboard[1]?.score}
				</span>
			</div>
		</div>

		<!-- top 3 -->
		<div class="absolute top-35 right-34 h-10 w-1">
			<div class="absolute top-0 left-11.5 h-full w-px">
				<div
					class={cn(
						'absolute -top-5 left-1/2 flex h-full w-fit -translate-x-1/2 flex-col items-center justify-center gap-3'
					)}
				>
					<div class="relative h-10 min-w-10">
						<img width="38" src="/assets/el/profile-icon-yellow.png" alt="profile" />
						<span
							class="font-parkin absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pt-1 text-center text-[22px] font-bold uppercase"
						>
							{leaderboard[2]?.name[0]}
						</span>
					</div>

					<span
						bind:this={top3NameElement}
						class="font-parkin mt-2 text-[18px] leading-0 font-bold tracking-widest text-nowrap uppercase"
					>
						{leaderboard[2]?.name || '--------'}
					</span>
				</div>
			</div>

			<div class="absolute top-29 left-9 w-px">
				<span
					class="font-parkin absolute left-1/2 -translate-x-1/2 pt-1.5 text-center text-[55.55px] font-bold uppercase"
				>
					{leaderboard[2]?.score}
				</span>
			</div>
		</div>
	</div>

	<div class="font-parkin grid grid-cols-3 gap-x-3 gap-y-10 pb-10">
		{#each leaderboard.slice(3) as data, index (data.id)}
			<div class="relative font-bold uppercase">
				<img class="drop-shadow-item" src="/assets/el/leaderboard-item.png" alt="item" />
				<!-- Number -->
				<span class="absolute top-1 left-10 text-[50px]">{index + 4}</span>

				<!-- Name + Score-->
				<div class="absolute top-1 left-28 flex w-73 items-center justify-between">
					<span class="text-[26px]">{data.name || '--------'}</span>
					<span class="text-[50px]">{data.score}</span>
				</div>
			</div>
		{/each}
	</div>

	<div
		class={cn(
			'breathing mt-20 flex items-center justify-center gap-6 opacity-100 transition-opacity duration-500',
			{
				'opacity-0': !showCountdown
			}
		)}
	>
		<img src="/assets/el/red-button.png" alt="button" />
		<span class="font-marqona text-[40px] tracking-widest text-white">
			Press any button to continue
		</span>
	</div>
</div>

<style>
	.drop-shadow-item {
		filter: drop-shadow(0px 12px 5.5px #000);
	}
</style>
