<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import { logEvent, startInteraction, verifyUser } from '$lib/api';
	import { appStore, resetGame } from '$lib/app.state';
	import { playAudio, stopAudio } from '$lib/audio';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { player1, player2, resetPlayers } from '$lib/player.state';
	import { writeToButtons, writeToLeds } from '$lib/serial-port';
	import { getRfidValue } from '$lib/services/rfid';
	import { onDestroy, onMount } from 'svelte';

	let lockPlayerTimer = $state<NodeJS.Timeout>();

	let listenRfidInterval = $state<NodeJS.Timeout>();
	let rfidScannedSuccessfully = $derived($player1.rfid || $player2.rfid);

	let showSinglePlayerReadyToGo = $state(false);
	let showMultiplayerReadyToGo = $state(false);

	let waitingCounterInterval = $state<NodeJS.Timeout>();
	let waitingCounter = $state(30);

	let title = $state('');

	let sendF = $state(false);

	$inspect(player1);
	$inspect(player2);

	function handleKeyPressed(key: string, pressedKey: number) {
		if (!rfidScannedSuccessfully) return;

		console.log('key presses: ', pressedKey);

		switch (pressedKey) {
			case 0: {
				break;
			}

			case 1: {
				if ($player1.rfid && $player2.rfid) {
					break;
				}

				console.log('game started as solo');

				$appStore.gameMode = 'solo';
				logEvent($appStore.interactionId, 'game started as solo');

				if (key === 'player1') {
					$player2.locked = true;
				} else {
					$player1.locked = true;
				}
				showSinglePlayerReadyToGo = true;
				if (waitingCounterInterval) clearInterval(waitingCounterInterval);

				setTimeout(() => {
					goto('/how-to-play');
				}, 3000);
				break;
			}

			case 2: {
				break;
			}
		}
	}

	function listenToPlayers() {
		const unsubPlayer1 = player1.subscribe(({ key, rfid, pressedKey }) => {
			if (pressedKey !== -1 && rfid && rfidScannedSuccessfully) {
				handleKeyPressed(key, pressedKey);
				$appStore.gameStartBy = 'player1';
			}
		});

		const unsubPlayer2 = player2.subscribe(({ key, rfid, pressedKey }) => {
			if (pressedKey !== -1 && rfid && rfidScannedSuccessfully) {
				handleKeyPressed(key, pressedKey);
				$appStore.gameStartBy = 'player2';
			}
		});

		return () => {
			unsubPlayer1();
			unsubPlayer2();
		};
	}

	function startWaitingCounter() {
		if (waitingCounterInterval) return;

		writeToButtons('o')

		waitingCounterInterval = setInterval(() => {
			waitingCounter--;
			if (waitingCounter <= 0) {
				clearInterval(waitingCounterInterval);
				waitingCounterInterval = undefined;

				//reset everything
				waitingCounter = 30;
				$player1.rfid = '';
				$player2.rfid = '';
				$player1.locked = false;
				$player2.locked = false;
				$appStore.gameMode = 'solo';
				$appStore.gameStartBy = '';
			}
		}, 1000);
	}

	onMount(() => {
		resetPlayers();
		resetGame();
	});

	onMount(() => {
		// bgm
		stopAudio('bgm2');
		playAudio('bgm');
		writeToLeds("e");

		setTimeout(
			() => {
				writeToButtons('m');
			},
			1000
		);

		const unsubscribe = listenToPlayers();

		return () => {
			unsubscribe();
			if (lockPlayerTimer) clearTimeout(lockPlayerTimer);
		};
	});

	onMount(async () => {
		const interaction = await startInteraction();
		$appStore.interactionId = interaction.id;

		listenRfidInterval = setInterval(async () => {
			const rfid = await getRfidValue();
			if (rfid[0] || rfid[1]) {
				startWaitingCounter();
			}

			if ($player1.rfid && $player2.rfid) {
				clearInterval(listenRfidInterval);

				showMultiplayerReadyToGo = true;
				$appStore.gameMode = 'multiplayer';
				logEvent($appStore.interactionId, 'game started as multiplayer');

				setTimeout(() => {
					goto('/how-to-play');
				}, 3000);
			}

			if (rfid[0] && rfid[0] !== $player1.rfid) {
				playAudio('scan');
				if (!sendF) writeToLeds("f");
				setTimeout(() => {writeToLeds("a")}, 300)
				setTimeout(() => {writeToLeds("b")}, 3000)
				const verifiedUser = await verifyUser(rfid[0]);
				$player1.rfid = verifiedUser.rfid;
				$player1.name = verifiedUser.name;
				$player1.id = verifiedUser.id;
				logEvent($appStore.interactionId, `[${verifiedUser.id}] Joined as ${verifiedUser.name}`);
				title = `${$player1.name} JOINED!`;
			}

			if (rfid[1] && rfid[1] !== $player2.rfid) {
				playAudio('scan');
				if (!sendF) writeToLeds("f");
				setTimeout(() => {writeToLeds("c")}, 300)
				setTimeout(() => {writeToLeds("d")}, 3000)
				const verifiedUser = await verifyUser(rfid[1]);
				$player2.rfid = verifiedUser.rfid;
				$player2.name = verifiedUser.name;
				$player2.id = verifiedUser.id;
				logEvent($appStore.interactionId, `[${verifiedUser.id}] Joined as ${verifiedUser.name}`);
				title = `${$player2.name} JOINED!`;
			}
		}, 1000);
	});

	onDestroy(() => {
		clearInterval(waitingCounterInterval);
		clearInterval(listenRfidInterval);
	});
</script>

{#if $player1.rfid || $player2.rfid}
	<h1
		class="font-parkin absolute top-37.5 left-1/2 -translate-x-1/2 text-[70px] leading-none font-extrabold tracking-wider text-white uppercase"
	>
		{title}
	</h1>

	<div class="absolute top-80 left-1/2 flex -translate-x-1/2 items-center justify-between gap-60">
		{#if !showSinglePlayerReadyToGo || (showSinglePlayerReadyToGo && $player1.rfid)}
			<div class="flex w-110 flex-col items-center justify-between">
				<div class="font-parkin relative h-125 w-110 text-white">
					<div class="relative z-10">
						<img src="/assets/el/player1-rfid-frame.png" alt="frame" />
						<div class="absolute top-17 left-19.5 h-[1px] w-[1px] bg-red-500">
							<span
								class="font-raleway absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[83.16px] leading-none font-bold tracking-widest uppercase"
							>
								{$player1.name[0] || $player1.key[0]}
							</span>
						</div>
						<div class="absolute top-9.5 left-35 w-64 truncate text-center">
							<span class="text-[42px] font-bold tracking-widest uppercase">
								{$player1.name}
							</span>
						</div>
					</div>
					<div
						class={cn('absolute top-1/2 left-1/2 z-0 w-185 -translate-x-1/2 -translate-y-1/2', {
							'opacity-50': !$player1.rfid
						})}
					>
						<img src="/assets/el/scan-idle.apng" alt="frame" />
					</div>
				</div>

				<div class="flex items-center gap-x-4">
					<div class="flex items-center gap-x-3">
						<span class="scale-up size-5 rounded-full bg-white"></span>
						<span class="scale-up ani-delay-1 size-5 rounded-full bg-white"></span>
						<span class="scale-up ani-delay-2 size-5 rounded-full bg-white"></span>
					</div>

					<p class="font-marqona text-[30px] leading-none tracking-widest text-white">
						{$player1.rfid ? 'READY' : `WAITING (${waitingCounter}s)`}
					</p>

					<div class="flex items-center gap-x-3">
						<span class="ani-delay-2 scale-up size-5 rounded-full bg-white"></span>
						<span class="ani-delay-1 scale-up size-5 rounded-full bg-white"></span>
						<span class="scale-up size-5 rounded-full bg-white"></span>
					</div>
				</div>
			</div>
		{/if}

		{#if !showSinglePlayerReadyToGo || (showSinglePlayerReadyToGo && $player2.rfid)}
			<div class="flex w-110 flex-col items-center justify-between">
				<div class="font-parkin relative h-125 w-110 text-white">
					<div class="relative z-10">
						<img src="/assets/el/player2-rfid-frame.png" alt="frame" />
						<div class="absolute top-17 left-19.5 h-[1px] w-[1px]">
							<span
								class="font-raleway -translate-y-1/2 absolute top-1/2 left-1/2 -translate-x-1/2 text-[83.16px] leading-none font-bold tracking-widest uppercase"
							>
								{$player2.name[0] || $player2.key[0]}
							</span>
						</div>
						<div class="absolute top-9.5 left-35 w-64 truncate text-center">
							<span class="text-[42px] font-bold tracking-widest uppercase">
								{$player2.name || $player2.key}
							</span>
						</div>
					</div>
					<div
						class={cn('absolute top-1/2 left-1/2 z-0 w-185 -translate-x-1/2 -translate-y-1/2', {
							'opacity-50': !$player2.rfid
						})}
					>
						<img src="/assets/el/scan-idle.apng" alt="frame" />
					</div>
				</div>

				<div class="flex items-center gap-x-4">
					<div class="flex items-center gap-x-3">
						<span class="scale-up size-5 rounded-full bg-white"></span>
						<span class="scale-up ani-delay-1 size-5 rounded-full bg-white"></span>
						<span class="scale-up ani-delay-2 size-5 rounded-full bg-white"></span>
					</div>

					<p class="font-marqona text-[30px] leading-none tracking-widest text-white">
						{$player2.rfid ? 'READY' : `WAITING (${waitingCounter}s)`}
					</p>

					<div class="flex items-center gap-x-3">
						<span class="ani-delay-2 scale-up size-5 rounded-full bg-white"></span>
						<span class="ani-delay-1 scale-up size-5 rounded-full bg-white"></span>
						<span class="scale-up size-5 rounded-full bg-white"></span>
					</div>
				</div>
			</div>
		{/if}
	</div>

	{#if !showSinglePlayerReadyToGo && !showMultiplayerReadyToGo}
		<div
			class="breathing absolute bottom-28 left-1/2 flex -translate-x-1/2 items-center justify-center gap-6"
		>
			<img src="/assets/el/blue-button.png" alt="blue" />
			<p class="font-marqona text-[40px] leading-none tracking-widest text-white">PLAY SOLO</p>
		</div>
	{/if}
{:else}
	<section class="bg-landing relative size-full">
		<!-- <button
			class="absolute top-14 left-20"
			onclick={() => console.log('lang')}
		>
			<img src="/assets/el/lang-switcher.png" alt="lang" />
		</button> -->
		<img
			class="absolute top-28 left-1/2 z-10 -translate-x-1/2"
			src="/assets/el/home-title.png"
			alt="title"
		/>

		<SimplePngSequencePlayer
			class="absolute top-0 left-0"
			imageUrls={Array.from(
				{ length: 810 },
				(_, i) => `/assets/pngSequences/title/${String(i).padStart(5, '0')}.png`
			)}
			fps={30}
			loop={true}
		/>

		<p
			class="font-phluff breathing absolute bottom-16 left-1/2 -translate-x-1/2 text-[36px] font-semibold text-white"
		>
			SCAN YOUR TAG TO START
		</p>
	</section>
{/if}

<style>
	.scale-up {
		animation: scale-up 0.6s ease-in-out infinite forwards;
	}

	.ani-delay-1 {
		animation-delay: 0.1s;
	}

	.ani-delay-2 {
		animation-delay: 0.15s;
	}

	@keyframes scale-up {
		0%,
		100% {
			transform: scale(0.2);
		}
		50% {
			transform: scale(1);
		}
	}
</style>
