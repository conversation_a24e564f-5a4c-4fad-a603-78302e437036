<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import { logEvent, submitScore } from '$lib/api';
	import { appStore } from '$lib/app.state';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { player1, player2 } from '$lib/player.state';
	import { writeToSerialPort } from '$lib/serial-port';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';

	let unsubscribe = $state(() => {});
	let showCTA = $state(false);
	let playerInstance = $appStore.gameStartBy === 'player1' ? player1 : player2;
	let extraScore = $derived(
		$playerInstance.results.reduce((acc, result) => acc + (result.isBonus ? 20 : 0), 0)
	);

	function subcribePlayers() {
		writeToSerialPort('o');

		const unsubPlayer1 = player1.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1) {
				goto('/leaderboard');
			}
		});

		const unsubPlayer2 = player2.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey !== -1) {
				goto('/leaderboard');
			}
		});

		return () => {
			unsubPlayer1();
			unsubPlayer2();
		};
	}

	onMount(() => {
		const timeout = setTimeout(() => {
			showCTA = true;
			unsubscribe = subcribePlayers();
		}, 1000);

		if ($appStore.gameMode === 'multiplayer') {
			const player1Bonus = $player1.results.reduce(
				(acc, result) => acc + (result.isBonus ? 20 : 0),
				0
			);
			const player2Bonus = $player2.results.reduce(
				(acc, result) => acc + (result.isBonus ? 20 : 0),
				0
			);

			submitScore($player1.id, $player1.score, {
				score: $player1.score,
				bonus: player1Bonus
			});
			submitScore($player2.id, $player2.score, {
				score: $player2.score,
				bonus: player2Bonus
			});
			logEvent(
				$appStore.interactionId,
				`[${$player1.name}] got ${$player1.score} points and ${player1Bonus} bonus points`
			);
			logEvent(
				$appStore.interactionId,
				`[${$player2.name}] got ${$player2.score} points and ${player2Bonus} bonus points`
			);
		} else {
			submitScore($playerInstance.id, $playerInstance.score, {
				score: $playerInstance.score,
				bonus: extraScore
			});
			logEvent(
				$appStore.interactionId,
				`[${$playerInstance.name}] got ${$playerInstance.score} points and ${extraScore} bonus points`
			);
		}

		return () => {
			unsubscribe();
			clearTimeout(timeout);
		};
	});
</script>

<div class="flex h-screen w-full flex-col items-center justify-center text-white" transition:fade>
	{#if $appStore.gameMode === 'solo'}
		<SimplePngSequencePlayer
			class="fixed top-0 left-0 h-screen w-screen"
			imageUrls={Array.from(
				{ length: 298 },
				(_, i) => `/assets/pngSequences/leaderboard-solo/${String(i).padStart(5, '0')}.png`
			)}
			fps={60}
			loop={true}
		/>

		<!-- Title -->
		<div
			class="fixed top-12 left-1/2 flex w-full -translate-x-1/2 items-center justify-center gap-14"
		>
			<SimplePngSequencePlayer
				imageUrls={Array.from(
					{ length: 45 },
					(_, i) => `/assets/pngSequences/lightning-top-left/${String(i).padStart(5, '0')}.png`
				)}
				fps={60}
				loop={true}
			/>
			<p
				class="font-parkin pt-16 text-[100px] leading-[100px] font-extrabold tracking-widest uppercase"
			>
				{#if $playerInstance.score >= 150}
					GREAT JOB!
				{:else if $playerInstance.score > 50}
					WELL DONE!
				{:else}
					NICE TRY!
				{/if}
			</p>

			<SimplePngSequencePlayer
				imageUrls={Array.from(
					{ length: 45 },
					(_, i) => `/assets/pngSequences/lightning-top-right/${String(i).padStart(5, '0')}.png`
				)}
				fps={60}
				loop={true}
			/>
		</div>

		<!-- Content -->
		<div class="fixed top-60 left-1/2 h-[680px] w-[1268px] -translate-x-1/2">
			<div class="absolute top-16 left-1/2 flex -translate-x-1/2 items-center justify-center gap-7">
				<div class="relative">
					<img width="146" src="/assets/el/profile-icon-yellow.png" alt="profile" />
					<span
						class="font-raleway absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[80px] leading-0 font-bold uppercase"
					>
						{$playerInstance.name[0]}
					</span>
				</div>

				<span class="font-parkin mt-15 text-[76px] leading-0 font-bold uppercase">
					{$playerInstance.name}
				</span>
			</div>

			<span
				class="font-parkin absolute top-68.5 left-70 text-center text-[60px] font-bold tracking-wider uppercase"
			>
				SCORE
			</span>

			<span
				class="font-parkin absolute top-108.5 left-70 text-center text-[60px] font-bold tracking-wider uppercase"
			>
				Rapid
			</span>

			<span
				class="font-parkin absolute top-64 right-62 text-center text-[80px] font-bold tracking-wider uppercase"
			>
				{$playerInstance.score}
			</span>

			<span
				class="font-parkin absolute top-104 right-62 text-center text-[80px] font-bold tracking-wider uppercase"
			>
				+{extraScore}
			</span>
		</div>
	{:else}
		<SimplePngSequencePlayer
			class="fixed top-0 left-0 h-screen w-screen"
			imageUrls={Array.from(
				{ length: 300 },
				(_, i) => `/assets/pngSequences/leaderboard-multiplayer/${String(i).padStart(5, '0')}.png`
			)}
			fps={60}
			loop={true}
		/>

		<!-- Title -->
		<div
			class="fixed top-32.5 left-1/2 flex w-full -translate-x-1/2 items-center justify-center gap-14"
		>
			{#if $player1.score > $player2.score || $player1.score < $player2.score}
				<img width="88" src="/assets/el/star.png" alt="star" class="animate-spin" />
			{/if}

			<p
				class="font-parkin pt-6 text-[100px] leading-[100px] font-extrabold tracking-widest uppercase"
			>
				{#if $player1.score > $player2.score}
					PLAYER 1 WINS!
				{:else if $player1.score < $player2.score}
					PLAYER 2 WINS!
				{:else}
					IT'S A TIE!
				{/if}
			</p>

			{#if $player1.score > $player2.score || $player1.score < $player2.score}
				<img width="88" src="/assets/el/star.png" alt="star" class="animate-spin" />
			{/if}
		</div>

		<!-- Content -->
		<div class="fixed top-60 left-1/2 h-[604px] w-[1593px] -translate-x-1/2">
			<div class="absolute top-14 left-45 flex items-center justify-center gap-4">
				<div class="relative">
					{#if $player1.score > $player2.score}
						<img
							class="absolute -top-14 -left-7"
							src="/assets/el/leaderboard-crown.png"
							alt="profile"
						/>
					{/if}
					<img width="115" src="/assets/el/profile-icon-yellow.png" alt="profile" />
					<span
						class="font-relaway absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[66.5px] leading-0 font-bold uppercase"
					>
						{$player1.name[0]}
					</span>
				</div>

				<span class="font-parkin mt-8 text-[60px] leading-0 font-bold uppercase">
					{$player1.name}
				</span>
			</div>

			<span
				class="font-parkin absolute top-65 left-54 text-center text-[50px] font-bold tracking-wider uppercase"
			>
				SCORE
			</span>

			<span
				class="font-parkin absolute top-100 left-54 text-center text-[50px] font-bold tracking-wider uppercase"
			>
				Rapid
			</span>

			<span
				class="font-parkin absolute top-58 right-235 text-center text-[74px] font-bold tracking-wider uppercase"
			>
				{$player1.score}
			</span>

			<span
				class="font-parkin absolute top-94 right-240 text-center text-[74px] font-bold tracking-wider uppercase"
			>
				+{$player1.results.reduce((acc, result) => acc + (result.isBonus ? 20 : 0), 0)}
			</span>

			<div class="absolute top-14 left-236 flex items-center justify-center gap-4">
				<div class="relative">
					{#if $player1.score < $player2.score}
						<img
							class="absolute -top-14 -left-7"
							src="/assets/el/leaderboard-crown.png"
							alt="profile"
						/>
					{/if}
					<img width="115" src="/assets/el/profile-icon-red.png" alt="profile" />
					<span
						class="font-relaway absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[66.5px] leading-0 font-bold uppercase"
					>
						{$player2.name[0]}
					</span>
				</div>

				<span class="font-parkin mt-8 text-[60px] leading-0 font-bold uppercase">
					{$player2.name}
				</span>
			</div>

			<span
				class="font-parkin absolute top-65 left-244 text-center text-[50px] font-bold tracking-wider uppercase"
			>
				SCORE
			</span>

			<span
				class="font-parkin absolute top-100 left-244 text-center text-[50px] font-bold tracking-wider uppercase"
			>
				Rapid
			</span>

			<span
				class="font-parkin absolute top-58 right-44 text-center text-[74px] font-bold tracking-wider uppercase"
			>
				{$player2.score}
			</span>

			<span
				class="font-parkin absolute top-94 right-48 text-center text-[74px] font-bold tracking-wider uppercase"
			>
				+{$player2.results.reduce((acc, result) => acc + (result.isBonus ? 20 : 0), 0)}
			</span>
		</div>
	{/if}

	<div
		class={cn(
			'breathing fixed bottom-18 flex items-center justify-center gap-6 opacity-100 transition-opacity duration-500',
			{
				'opacity-0': !showCTA
			}
		)}
	>
		<img src="/assets/el/red-button.png" alt="button" />
		<span class="font-marqona text-[40px] tracking-widest text-white">
			Press any button to continue
		</span>
	</div>
</div>
