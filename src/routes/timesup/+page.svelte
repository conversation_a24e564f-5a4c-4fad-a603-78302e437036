<script lang="ts">
	import { goto } from '$app/navigation';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { player1, player2 } from '$lib/player.state';
	import { writeToButtons } from '$lib/serial-port';
	import { onMount } from 'svelte';

	function subcribePlayers() {
		writeToButtons('o');

		const unsubPlayer1 = player1.subscribe(({ pressedKey, locked }) => {
			if (locked) return;


			if (pressedKey === 2) {
				goto('/score');
			}
		});

		const unsubPlayer2 = player2.subscribe(({ pressedKey, locked }) => {
			if (locked) return;

			if (pressedKey === 2) {
				goto('/score');
			}
		});

		return () => {
			unsubPlayer1();
			unsubPlayer2();
		};
	}

	onMount(() => {
		const unsubscribe = subcribePlayers();

		const interval = setInterval(() => {
			writeToButtons("o")
		}, 2000)

		return () => {
			clearInterval(interval)
			unsubscribe();
		};
	});
</script>

<div
	class="relative flex size-full flex-col items-center justify-center bg-cover bg-center bg-no-repeat"
>
	<div
		class="fixed top-12 left-1/2 flex w-3/5 -translate-x-1/2 items-center justify-between gap-14"
	>
		<SimplePngSequencePlayer
			class="mt-10"
			imageUrls={Array.from(
				{ length: 45 },
				(_, i) => `/assets/pngSequences/lightning-top-left/${String(i).padStart(5, '0')}.png`
			)}
			fps={60}
			loop={true}
		/>

		<SimplePngSequencePlayer
			imageUrls={Array.from(
				{ length: 45 },
				(_, i) => `/assets/pngSequences/lightning-top-right/${String(i).padStart(5, '0')}.png`
			)}
			fps={60}
			loop={true}
		/>
	</div>

	<img class="scale-in-out" src="/assets/el/gameover.png" alt="timesuptext" />

	<div class="fade-in flex flex-col items-center justify-center gap-23.5">
		<img width="280" src="/assets/el/kidney.png" alt="kidney" />

		<p class="font-phluff text-center text-[26px] font-medium tracking-wider text-white">
			Your kidneys work hard to keep you healthy, but diabetes or high blood <br /> pressure can harm
			them—so keep them strong with healthy choices!
		</p>

		<div class="breathing flex items-center justify-center gap-6">
			<img src="/assets/el/white-button.png" alt="button" />
			<span class="font-marqona text-[40px] tracking-widest text-white"> SKIP TO SCORE </span>
		</div>
	</div>
</div>

<style>
	.scale-in-out {
		position: absolute;
		top: 50%;
		transform: scale(0.5) translateY(-50%);
		animation: scale-in-out 2s ease-in-out forwards;
	}

	@keyframes scale-in-out {
		0% {
			top: 50%;
			transform: scale(0.5) translateY(-50%);
		}
		33% {
			top: 50%;
			transform: scale(1.5) translateY(-50%);
			opacity: 0;
		}
		66% {
			top: 50%;
			transform: scale(1) translateY(-50%);
			opacity: 0;
		}
		70% {
			top: 50%;
			transform: scale(1) translateY(-50%);
			opacity: 0;
		}
		100% {
			top: 150px;
			transform: scale(0.7);
			opacity: 0;
		}
	}

	.fade-in {
		position: absolute;
		top: 28%;
		opacity: 0;
		animation: 2s fade-in 1s ease-in-out forwards;
	}

	@keyframes fade-in {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}
</style>
