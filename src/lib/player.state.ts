import { writable } from 'svelte/store';

export interface Player {
	key: string;
	id: string;
	name: string;
	pressedKey: number;
	locked: boolean;
	rfid: string;
	score: number;
	results: {
		questions: string;
		answer: number;
		correctAnswer: number;
		isCorrect: boolean;
		isBonus: boolean;
		currentTime?: number;
	}[];
}

export const player1 = writable<Player>({
	key: 'player1',
	id: '',
	name: 'W',
	pressedKey: -1,
	locked: false,
	rfid: '',
	score: 0,
	results: []
});

const unsubP1 = player1.subscribe(({ rfid }) => {
	if (rfid) {
		console.log('found: ', rfid);
		unsubP1();
	}
});

export const player2 = writable<Player>({
	key: 'player2',
	id: '',
	name: 'R',
	pressedKey: -1,
	locked: false,
	rfid: '',
	score: 0,
	results: []
});

const unsubP2 = player2.subscribe(({ rfid }) => {
	if (rfid) {
		console.log('found: ', rfid);
		unsubP2();
	}
});

export const resetPlayers = () => {
	player1.update((player) => {
		player.id = '';
		player.name = '';
		player.pressedKey = -1;
		player.locked = false;
		player.rfid = '';
		player.score = 0;
		player.results = [];
		return player;
	});

	player2.update((player) => {
		player.id = '';
		player.name = '';
		player.pressedKey = -1;
		player.locked = false;
		player.rfid = '';
		player.score = 0;
		player.results = [];
		return player;
	});
};
