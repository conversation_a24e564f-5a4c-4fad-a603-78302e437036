/**
 * Multi-Port Serial Example
 *
 * This example demonstrates how to use the new multi-port serial API
 * to connect to multiple COM ports simultaneously.
 */

import {
	// New Multi-Port API
	connectToPort,
	writeToPort,
	getPortDataById,
	isPortReadyById,
	listConnectedPorts,
	setDefaultPort,
	disconnectAllPorts,
	forceReconnectPort,

	// Reactive Stores
	portDataMap,
	portErrorMap,
	multiPortState,

	// Legacy API (still works)
	connectToSerialPort,
	writeToSerialPort,
	serialPortData
} from '$lib/serial-port';

/**
 * Example 1: Basic Multi-Port Usage
 */
export async function basicMultiPortExample() {
	console.log('=== Basic Multi-Port Example ===');

	// Connect to multiple ports with different configurations
	const player1Connected = await connectToPort('player1', { baudRate: 9600 });
	const player2Connected = await connectToPort('player2', { baudRate: 115200 });
	const sensorsConnected = await connectToPort('sensors', { baudRate: 57600 });

	console.log('Player 1 connected:', player1Connected);
	console.log('Player 2 connected:', player2Connected);
	console.log('Sensors connected:', sensorsConnected);

	// List all connected ports
	const connectedPorts = listConnectedPorts();
	console.log('Connected ports:', connectedPorts);

	// Send different commands to different ports
	if (player1Connected) {
		await writeToPort('player1', 'PLAYER1_READY');
	}

	if (player2Connected) {
		await writeToPort('player2', 'PLAYER2_READY');
	}

	if (sensorsConnected) {
		await writeToPort('sensors', 'START_MONITORING');
	}

	// Read data from specific ports
	setTimeout(() => {
		const player1Data = getPortDataById('player1');
		const player2Data = getPortDataById('player2');
		const sensorData = getPortDataById('sensors');

		console.log('Player 1 data:', player1Data);
		console.log('Player 2 data:', player2Data);
		console.log('Sensor data:', sensorData);
	}, 1000);
}

/**
 * Example 2: Using Reactive Stores
 */
export function reactiveStoreExample() {
	console.log('=== Reactive Store Example ===');

	// Subscribe to all port data changes
	const unsubscribeData = portDataMap.subscribe((dataMap) => {
		console.log('=== Port Data Update ===');
		for (const [portId, data] of dataMap) {
			console.log(`${portId}:`, data);
		}
	});

	// Subscribe to port errors
	const unsubscribeErrors = portErrorMap.subscribe((errorMap) => {
		for (const [portId, error] of errorMap) {
			if (error) {
				console.error(`Port ${portId} error:`, error.message);
			}
		}
	});

	// Subscribe to overall multi-port state
	const unsubscribeState = multiPortState.subscribe((state) => {
		console.log('Total connections:', state.connections.size);
		console.log('Default port:', state.defaultPortId);
	});

	// Return cleanup function
	return () => {
		unsubscribeData();
		unsubscribeErrors();
		unsubscribeState();
	};
}

/**
 * Example 3: Game Controller Setup
 */
export class GameController {
	private cleanupFunctions: (() => void)[] = [];

	async initialize() {
		console.log('=== Game Controller Setup ===');

		// Connect to game devices
		await connectToPort('player1_controller', { baudRate: 9600 });
		await connectToPort('player2_controller', { baudRate: 9600 });
		await connectToPort('display_board', { baudRate: 115200 });
		await connectToPort('sound_system', { baudRate: 57600 });

		// Set player1 as default for legacy code compatibility
		setDefaultPort('player1_controller');

		// Set up monitoring
		this.setupDataMonitoring();
		this.setupErrorHandling();

		console.log('Game controller initialized');
	}

	private setupDataMonitoring() {
		const cleanup = portDataMap.subscribe((dataMap) => {
			const player1Data = dataMap.get('player1_controller');
			const player2Data = dataMap.get('player2_controller');

			if (player1Data) {
				this.handlePlayerInput('player1', player1Data);
			}

			if (player2Data) {
				this.handlePlayerInput('player2', player2Data);
			}
		});

		this.cleanupFunctions.push(cleanup);
	}

	private setupErrorHandling() {
		const cleanup = portErrorMap.subscribe((errorMap) => {
			for (const [portId, error] of errorMap) {
				if (error) {
					console.error(`Device ${portId} error:`, error.message);
					this.handleDeviceError(portId, error);
				}
			}
		});

		this.cleanupFunctions.push(cleanup);
	}

	private handlePlayerInput(player: string, data: any) {
		console.log(`${player} input:`, data);

		// Example: Check for button presses
		const [btn1, btn2, btn3, btn4, btn5, btn6] = data;

		if (btn1 === '1') {
			console.log(`${player} pressed button 1`);
			this.updateDisplay(`${player} - Button 1`);
		}

		if (btn2 === '1') {
			console.log(`${player} pressed button 2`);
			this.playSound('button_press');
		}
	}

	private async handleDeviceError(portId: string, error: any) {
		console.log(`Attempting to recover device: ${portId}`);

		// Try to reconnect
		const reconnected = await forceReconnectPort(portId);
		if (reconnected) {
			console.log(`Successfully reconnected ${portId}`);
		} else {
			console.error(`Failed to reconnect ${portId}`);
		}
	}

	async updateDisplay(message: string) {
		if (isPortReadyById('display_board')) {
			await writeToPort('display_board', `DISPLAY:${message}`);
		}
	}

	async playSound(soundId: string) {
		if (isPortReadyById('sound_system')) {
			await writeToPort('sound_system', `PLAY:${soundId}`);
		}
	}

	async startGame() {
		console.log('Starting game...');

		// Send start commands to all devices
		await writeToPort('player1_controller', 'GAME_START');
		await writeToPort('player2_controller', 'GAME_START');
		await writeToPort('display_board', 'DISPLAY:GAME STARTED');
		await writeToPort('sound_system', 'PLAY:game_start');
	}

	async endGame() {
		console.log('Ending game...');

		// Send end commands
		await writeToPort('player1_controller', 'GAME_END');
		await writeToPort('player2_controller', 'GAME_END');
		await writeToPort('display_board', 'DISPLAY:GAME OVER');
		await writeToPort('sound_system', 'PLAY:game_end');
	}

	async cleanup() {
		console.log('Cleaning up game controller...');

		// Clean up subscriptions
		this.cleanupFunctions.forEach((cleanup) => cleanup());
		this.cleanupFunctions = [];

		// Disconnect all devices
		await disconnectAllPorts();

		console.log('Game controller cleaned up');
	}
}

/**
 * Example 4: Backward Compatibility
 */
export async function backwardCompatibilityExample() {
	console.log('=== Backward Compatibility Example ===');

	// This is the old way - still works perfectly
	const connected = await connectToSerialPort({ baudRate: 9600 });
	console.log('Legacy connection:', connected);

	if (connected) {
		await writeToSerialPort('LEGACY_COMMAND');

		// Legacy store subscription still works
		const unsubscribe = serialPortData.subscribe((data) => {
			console.log('Legacy data:', data);
		});

		// Clean up after 5 seconds
		setTimeout(() => {
			unsubscribe();
		}, 5000);
	}
}

/**
 * Example Usage
 */
export async function runExamples() {
	try {
		// Run basic example
		await basicMultiPortExample();

		// Set up reactive monitoring
		const cleanupReactive = reactiveStoreExample();

		// Initialize game controller
		const gameController = new GameController();
		await gameController.initialize();
		await gameController.startGame();

		// Wait a bit then end game
		setTimeout(async () => {
			await gameController.endGame();
			await gameController.cleanup();
			cleanupReactive();
		}, 10000);

		// Show backward compatibility
		await backwardCompatibilityExample();
	} catch (error) {
		console.error('Example error:', error);
	}
}

// Uncomment to run examples
// runExamples();
