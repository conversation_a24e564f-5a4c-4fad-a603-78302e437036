import { dev } from '$app/environment';
import { writable } from 'svelte/store';

// Enhanced types for better error handling
export interface SerialPortConfig {
	baudRate: number;
	dataBits?: 7 | 8;
	stopBits?: 1 | 2;
	parity?: 'none' | 'even' | 'odd';
	bufferSize?: number;
	flowControl?: 'none' | 'hardware';
}

export interface SerialPortState {
	isConnected: boolean;
	isConnecting: boolean;
	isReading: boolean;
	isWriting: boolean;
	error: string | null;
	lastActivity: Date | null;
}

export interface SerialPortError {
	type:
		| 'connection'
		| 'permission'
		| 'timeout'
		| 'data'
		| 'browser_support'
		| 'port_busy'
		| 'unknown';
	message: string;
	originalError?: Error;
	timestamp: Date;
}

// Multi-port support types
export interface SerialPortConnection {
	id: string;
	port: SerialPort;
	config: SerialPortConfig;
	state: SerialPortState;
	data: SerialData;
	error: SerialPortError | null;
	reader: ReadableStreamDefaultReader<Uint8Array> | null;
	writer: WritableStreamDefaultWriter<Uint8Array> | null;
	reconnectAttempts: number;
	isCleaningUp: boolean;
}

export interface MultiPortState {
	connections: Map<string, SerialPortConnection>;
	defaultPortId: string | null;
}

// Default configuration
const DEFAULT_CONFIG: SerialPortConfig = {
	baudRate: 9600,
	dataBits: 8,
	stopBits: 1,
	parity: 'none',
	bufferSize: 255,
	flowControl: 'none'
};

// Store definitions
const portState = writable<SerialPortState>({
	isConnected: false,
	isConnecting: false,
	isReading: false,
	isWriting: false,
	error: null,
	lastActivity: null
});

export type SerialValue = '0' | '1' | '2';
export type SerialData = [
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue
];

// Legacy stores for backward compatibility
export const serialPortData = writable<SerialData>(['0', '0', '0', '0', '0', '0']);
export const serialPortError = writable<SerialPortError | null>(null);

// New multi-port stores
export const multiPortState = writable<MultiPortState>({
	connections: new Map(),
	defaultPortId: null
});
export const portDataMap = writable<Map<string, SerialData>>(new Map());
export const portErrorMap = writable<Map<string, SerialPortError | null>>(new Map());



// Utility functions
const isWebSerialSupported = (): boolean => {
	return typeof navigator !== 'undefined' && 'serial' in navigator;
};

const createError = (
	type: SerialPortError['type'],
	message: string,
	originalError?: Error
): SerialPortError => {
	return {
		type,
		message,
		originalError,
		timestamp: new Date()
	};
};

// Simple Dual-Port Manager for Buttons and LEDs
export interface DualPortState {
	buttonsConnected: boolean;
	ledsConnected: boolean;
	buttonsData: SerialData;
	ledsData: SerialData;
	buttonsError: SerialPortError | null;
	ledsError: SerialPortError | null;
	isConfigured: boolean;
}

// Port configuration storage
interface PortConfig {
	buttonsPortInfo: Partial<SerialPortInfo> | null;
	ledsPortInfo: Partial<SerialPortInfo> | null;
}

const PORT_CONFIG_KEY = 'dual_port_serial_config';

// Stores for dual-port system
export const buttonsPortData = writable<SerialData>(['0', '0', '0', '0', '0', '0']);
export const ledsPortData = writable<SerialData>(['0', '0', '0', '0', '0', '0']);
export const buttonsPortError = writable<SerialPortError | null>(null);
export const ledsPortError = writable<SerialPortError | null>(null);
export const dualPortState = writable<DualPortState>({
	buttonsConnected: false,
	ledsConnected: false,
	buttonsData: ['0', '0', '0', '0', '0', '0'],
	ledsData: ['0', '0', '0', '0', '0', '0'],
	buttonsError: null,
	ledsError: null,
	isConfigured: false
});

class DualPortSerialManager {
	private buttonsPort: SerialPort | null = null;
	private ledsPort: SerialPort | null = null;
	private buttonsReader: ReadableStreamDefaultReader<Uint8Array> | null = null;
	private ledsReader: ReadableStreamDefaultReader<Uint8Array> | null = null;

	// Save port configuration to localStorage
	private savePortConfig(buttonsPort: SerialPort, ledsPort: SerialPort) {
		try {
			const config: PortConfig = {
				buttonsPortInfo: buttonsPort.getInfo(),
				ledsPortInfo: ledsPort.getInfo()
			};
			localStorage.setItem(PORT_CONFIG_KEY, JSON.stringify(config));
			this.updateDualPortState({ isConfigured: true });
			console.log('Port configuration saved');
		} catch (error) {
			console.warn('Failed to save port configuration:', error);
		}
	}

	// Load port configuration from localStorage
	private loadPortConfig(): PortConfig | null {
		try {
			const configStr = localStorage.getItem(PORT_CONFIG_KEY);
			if (!configStr) return null;

			const config = JSON.parse(configStr) as PortConfig;
			return config;
		} catch (error) {
			console.warn('Failed to load port configuration:', error);
			return null;
		}
	}

	// Check if ports are configured
	isConfigured(): boolean {
		return this.loadPortConfig() !== null;
	}

	// Clear saved configuration
	clearConfiguration() {
		try {
			localStorage.removeItem(PORT_CONFIG_KEY);
			this.updateDualPortState({ isConfigured: false });
			console.log('Port configuration cleared');
		} catch (error) {
			console.warn('Failed to clear port configuration:', error);
		}
	}

	// Find matching port from available ports
	private async findMatchingPort(targetInfo: Partial<SerialPortInfo> | null): Promise<SerialPort | null> {
		if (!targetInfo) return null;
		try {
			const availablePorts = await navigator.serial.getPorts();

			console.log(`Available ports: `, availablePorts)

			for (const port of availablePorts) {
				const portInfo = port.getInfo();

				console.log(`Port info: `, portInfo)


				// Match by USB vendor/product ID if available
				if (targetInfo.usbVendorId && targetInfo.usbProductId) {
					if (portInfo.usbVendorId === targetInfo.usbVendorId &&
						portInfo.usbProductId === targetInfo.usbProductId) {
						return port;
					}
				}
			}

			return null;
		} catch (error) {
			console.warn('Failed to find matching port:', error);
			return null;
		}
	}

	// Auto-connect using saved configuration
	async autoConnect(): Promise<{ buttons: boolean; leds: boolean }> {
		// if (dev) {
		// 	// Mock connections in development
		// 	this.updateDualPortState({
		// 		buttonsConnected: true,
		// 		ledsConnected: true,
		// 		isConfigured: true
		// 	});
		// 	return { buttons: true, leds: true };
		// }

		const config = this.loadPortConfig();
		if (!config || !config.buttonsPortInfo || !config.ledsPortInfo) {
			console.log('No saved configuration found');
			return { buttons: false, leds: false };
		}

		if (!isWebSerialSupported()) {
			const error = createError('browser_support', 'Web Serial API is not supported in this browser');
			buttonsPortError.set(error);
			ledsPortError.set(error);
			return { buttons: false, leds: false };
		}

		try {
			// Find matching ports
			const buttonsPort = await this.findMatchingPort(config.buttonsPortInfo);
			const ledsPort = await this.findMatchingPort(config.ledsPortInfo);

			if (!buttonsPort) {
				console.warn('Buttons port not found, may need reconfiguration');
				return { buttons: false, leds: false };
			}

			if (!ledsPort) {
				console.warn('LEDs port not found, may need reconfiguration');
				return { buttons: false, leds: false };
			}

			// Connect to both ports
			const buttonsResult = await this.connectToPort('buttons', buttonsPort);
			const ledsResult = await this.connectToPort('leds', ledsPort);

			if (buttonsResult && ledsResult) {
				this.updateDualPortState({ isConfigured: true });
			}

			return { buttons: buttonsResult, leds: ledsResult };
		} catch (error) {
			console.error('Auto-connect failed:', error);
			return { buttons: false, leds: false };
		}
	}

	// Show port selection popup and connect to both ports (for initial setup or reconfiguration)
	async configureAndConnect(): Promise<{ buttons: boolean; leds: boolean }> {
		// if (dev) {
		// 	// Mock connections in development
		// 	this.updateDualPortState({
		// 		buttonsConnected: true,
		// 		ledsConnected: true,
		// 		isConfigured: true
		// 	});
		// 	return { buttons: true, leds: true };
		// }

		if (!isWebSerialSupported()) {
			const error = createError('browser_support', 'Web Serial API is not supported in this browser');
			buttonsPortError.set(error);
			ledsPortError.set(error);
			return { buttons: false, leds: false };
		}

		try {
			// Show custom port selection dialog
			const portSelection = await this.showPortSelectionDialog();
			if (!portSelection) {
				return { buttons: false, leds: false };
			}

			const { buttonsPort, ledsPort } = portSelection;

			// Connect to both ports
			const buttonsResult = await this.connectToPort('buttons', buttonsPort);
			const ledsResult = await this.connectToPort('leds', ledsPort);

			// Save configuration if both connections successful
			if (buttonsResult && ledsResult) {
				this.savePortConfig(buttonsPort, ledsPort);
			}

			return { buttons: buttonsResult, leds: ledsResult };
		} catch (error) {
			console.error('Failed to configure and connect to ports:', error);
			return { buttons: false, leds: false };
		}
	}

	// Smart connect: auto-connect if configured, otherwise show configuration dialog
	async connectToBothPorts(): Promise<{ buttons: boolean; leds: boolean }> {
		// First try auto-connect if already configured
		if (this.isConfigured()) {
			console.log('Attempting auto-connect using saved configuration...');
			const result = await this.autoConnect();

			// If auto-connect successful, return
			if (result.buttons && result.leds) {
				console.log('Auto-connect successful');
				return result;
			}

			// If auto-connect failed, fall back to configuration dialog
			console.log('Auto-connect failed, showing configuration dialog...');
		} else {
			console.log('No configuration found, showing setup dialog...');
		}

		// Show configuration dialog
		return await this.configureAndConnect();
	}

	// Show custom port selection dialog
	private async showPortSelectionDialog(): Promise<{ buttonsPort: SerialPort; ledsPort: SerialPort } | null> {
		try {
			// Get available ports
			const availablePorts = await navigator.serial.getPorts();

			if (availablePorts.length < 2) {
				// Need to request ports from user
				const port1 = await navigator.serial.requestPort();
				const port2 = await navigator.serial.requestPort();

				// Show selection dialog
				return await this.showPortAssignmentDialog([port1, port2]);
			} else {
				// Show selection dialog with available ports
				return await this.showPortAssignmentDialog(availablePorts);
			}
		} catch (error) {
			console.error('Port selection cancelled or failed:', error);
			return null;
		}
	}

	// Show dialog to assign ports to buttons/leds
	private async showPortAssignmentDialog(ports: SerialPort[]): Promise<{ buttonsPort: SerialPort; ledsPort: SerialPort } | null> {
		return new Promise((resolve) => {
			// Create modal dialog
			const modal = document.createElement('div');
			modal.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0.8);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 10000;
				font-family: Arial, sans-serif;
			`;

			const dialog = document.createElement('div');
			dialog.style.cssText = `
				background: white;
				padding: 30px;
				border-radius: 10px;
				max-width: 500px;
				width: 90%;
				box-shadow: 0 10px 30px rgba(0,0,0,0.3);
			`;

			dialog.innerHTML = `
				<h2 style="margin-top: 0; color: #333;">Select COM Ports</h2>
				<p style="color: #666; margin-bottom: 20px;">Please assign the COM ports to their respective functions:</p>

				<div style="margin-bottom: 20px;">
					<label style="display: block; margin-bottom: 10px; font-weight: bold; color: #333;">
						🎮 Buttons Port:
						<select id="buttonsPortSelect" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
							${ports.map((port, index) => {
								const info = port.getInfo();
								const label = `Port ${index + 1} (VID: ${info.usbVendorId || 'Unknown'}, PID: ${info.usbProductId || 'Unknown'})`;
								return `<option value="${index}">${label}</option>`;
							}).join('')}
						</select>
					</label>
				</div>

				<div style="margin-bottom: 30px;">
					<label style="display: block; margin-bottom: 10px; font-weight: bold; color: #333;">
						💡 LEDs Port:
						<select id="ledsPortSelect" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
							${ports.map((port, index) => {
								const info = port.getInfo();
								const label = `Port ${index + 1} (VID: ${info.usbVendorId || 'Unknown'}, PID: ${info.usbProductId || 'Unknown'})`;
								return `<option value="${index}">${label}</option>`;
							}).join('')}
						</select>
					</label>
				</div>

				<div style="text-align: right;">
					<button id="cancelBtn" style="padding: 10px 20px; margin-right: 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">Cancel</button>
					<button id="connectBtn" style="padding: 10px 20px; border: none; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">Connect</button>
				</div>
			`;

			modal.appendChild(dialog);
			document.body.appendChild(modal);

			// Handle button clicks
			const buttonsSelect = dialog.querySelector('#buttonsPortSelect') as HTMLSelectElement;
			const ledsSelect = dialog.querySelector('#ledsPortSelect') as HTMLSelectElement;
			const cancelBtn = dialog.querySelector('#cancelBtn') as HTMLButtonElement;
			const connectBtn = dialog.querySelector('#connectBtn') as HTMLButtonElement;

			// Set different default selections
			if (ports.length >= 2) {
				buttonsSelect.selectedIndex = 0;
				ledsSelect.selectedIndex = 1;
			}

			cancelBtn.onclick = () => {
				document.body.removeChild(modal);
				resolve(null);
			};

			connectBtn.onclick = () => {
				const buttonsIndex = parseInt(buttonsSelect.value);
				const ledsIndex = parseInt(ledsSelect.value);

				if (buttonsIndex === ledsIndex) {
					alert('Please select different ports for buttons and LEDs');
					return;
				}

				document.body.removeChild(modal);
				resolve({
					buttonsPort: ports[buttonsIndex],
					ledsPort: ports[ledsIndex]
				});
			};

			// Handle escape key
			const handleEscape = (e: KeyboardEvent) => {
				if (e.key === 'Escape') {
					document.body.removeChild(modal);
					document.removeEventListener('keydown', handleEscape);
					resolve(null);
				}
			};
			document.addEventListener('keydown', handleEscape);
		});
	}

	// Connect to a specific port (buttons or leds)
	private async connectToPort(type: 'buttons' | 'leds', port: SerialPort): Promise<boolean> {
		try {
			const config = { ...DEFAULT_CONFIG, baudRate: 9600 };

			// Open the port
			if (!port.readable || !port.writable) {
				await port.open(config);
			}

			// Store the port
			if (type === 'buttons') {
				this.buttonsPort = port;
			} else {
				this.ledsPort = port;
			}

			// Start listening for data
			this.startListening(type);

			// Update state
			this.updateDualPortState({
				[`${type}Connected`]: true
			});

			console.log(`Successfully connected to ${type} port`);
			return true;
		} catch (error) {
			console.error(`Failed to connect to ${type} port:`, error);
			const serialError = createError('connection', `Failed to connect to ${type} port`, error instanceof Error ? error : new Error('Unknown error'));

			if (type === 'buttons') {
				buttonsPortError.set(serialError);
			} else {
				ledsPortError.set(serialError);
			}

			return false;
		}
	}

	// Start listening for data on a specific port
	private async startListening(type: 'buttons' | 'leds'): Promise<void> {
		const port = type === 'buttons' ? this.buttonsPort : this.ledsPort;
		if (!port || !port.readable) return;

		try {
			const reader = port.readable.getReader();
			if (type === 'buttons') {
				this.buttonsReader = reader;
			} else {
				this.ledsReader = reader;
			}

			let receivedData = '';

			while (true) {
				const result = await reader.read();

				const { value, done } = result;

				if (done) break;
				if (!value || value.length === 0) continue;

				// Decode received data
				const decoder = new TextDecoder('utf-8', { fatal: false });
				const chunk = decoder.decode(value, { stream: true });
				receivedData += chunk;

				// Process complete lines
				const lines = receivedData.split('\n');
				receivedData = lines.pop() || '';

				for (const line of lines) {
					const trimmedLine = line.trim();
					if (trimmedLine.length === 0) continue;

					// Validate and process the data
					try {
						if (
							trimmedLine.split(',').length !== 6 ||
							trimmedLine.split(',').some((v) => !['0', '1', '2'].includes(v))
						) {
							console.warn(`Invalid data format from ${type}:`, trimmedLine);
							continue;
						}

						const data = trimmedLine.split(',') as SerialData;

						// Update appropriate store
						if (type === 'buttons') {
							buttonsPortData.set(data);
						} else {
							ledsPortData.set(data);
						}

						// Update dual port state
						this.updateDualPortState({
							[`${type}Data`]: data
						});

						console.log(`Received ${type} data:`, trimmedLine);
					} catch (parseError) {
						console.warn(`Failed to parse ${type} data:`, trimmedLine, parseError);
					}
				}
			}
		} catch (error) {
			console.error(`Error reading from ${type} port:`, error);
			const serialError = createError('data', `Error reading from ${type} port`, error instanceof Error ? error : new Error('Unknown error'));

			if (type === 'buttons') {
				buttonsPortError.set(serialError);
			} else {
				ledsPortError.set(serialError);
			}
		}
	}

	// Update dual port state
	private updateDualPortState(updates: Partial<DualPortState>) {
		dualPortState.update(state => ({ ...state, ...updates }));
	}

	// Write to buttons port
	async writeToButtons(data: string): Promise<boolean> {
		return this.writeToPortType('buttons', data);
	}

	// Write to LEDs port
	async writeToLeds(data: string): Promise<boolean> {
		return this.writeToPortType('leds', data);
	}

	// Write to specific port type
	private async writeToPortType(type: 'buttons' | 'leds', data: string): Promise<boolean> {
		const port = type === 'buttons' ? this.buttonsPort : this.ledsPort;
		if (!port || !port.writable) {
			console.error(`${type} port is not connected or writable`);
			return false;
		}

		try {
			const writer = port.writable.getWriter();
			const encodedData = new TextEncoder().encode(data);
			await writer.write(encodedData);
			writer.releaseLock();

			console.log(`Successfully wrote to ${type} port:`, data);
			return true;
		} catch (error) {
			console.error(`Failed to write to ${type} port:`, error);
			const serialError = createError('data', `Failed to write to ${type} port`, error instanceof Error ? error : new Error('Unknown error'));

			if (type === 'buttons') {
				buttonsPortError.set(serialError);
			} else {
				ledsPortError.set(serialError);
			}

			return false;
		}
	}

	// Disconnect both ports
	async disconnectBothPorts(): Promise<void> {
		await Promise.all([
			this.disconnectPort('buttons'),
			this.disconnectPort('leds')
		]);
	}

	// Disconnect specific port
	private async disconnectPort(type: 'buttons' | 'leds'): Promise<void> {
		try {
			// Close reader
			const reader = type === 'buttons' ? this.buttonsReader : this.ledsReader;
			if (reader) {
				await reader.cancel();
				reader.releaseLock();
				if (type === 'buttons') {
					this.buttonsReader = null;
				} else {
					this.ledsReader = null;
				}
			}

			// Close port
			const port = type === 'buttons' ? this.buttonsPort : this.ledsPort;
			if (port) {
				await port.close();
				if (type === 'buttons') {
					this.buttonsPort = null;
				} else {
					this.ledsPort = null;
				}
			}

			// Update state
			this.updateDualPortState({
				[`${type}Connected`]: false,
				[`${type}Error`]: null
			});

			console.log(`Disconnected from ${type} port`);
		} catch (error) {
			console.error(`Error disconnecting ${type} port:`, error);
		}
	}

	// Get connection status
	isButtonsConnected(): boolean {
		return this.buttonsPort !== null;
	}

	isLedsConnected(): boolean {
		return this.ledsPort !== null;
	}

	areBothConnected(): boolean {
		return this.isButtonsConnected() && this.isLedsConnected();
	}
}

// Create global dual-port manager instance
const dualPortManager = new DualPortSerialManager();

// New Dual-Port API Functions

// Smart connect: auto-connect if configured, otherwise show configuration dialog
export const connectToBothPorts = (): Promise<{ buttons: boolean; leds: boolean }> => {
	return dualPortManager.connectToBothPorts();
};

// Auto-connect using saved configuration (no popup)
export const autoConnect = (): Promise<{ buttons: boolean; leds: boolean }> => {
	return dualPortManager.autoConnect();
};

// Show configuration dialog and connect (for initial setup or reconfiguration)
export const configureAndConnect = (): Promise<{ buttons: boolean; leds: boolean }> => {
	return dualPortManager.configureAndConnect();
};

// Check if ports are configured
export const isConfigured = (): boolean => {
	return dualPortManager.isConfigured();
};

// Clear saved configuration
export const clearConfiguration = (): void => {
	return dualPortManager.clearConfiguration();
};

export const writeToButtons = (data: string): Promise<boolean> => {
	return dualPortManager.writeToButtons(data);
};

export const writeToLeds = (data: string): Promise<boolean> => {
	return dualPortManager.writeToLeds(data);
};

export const disconnectBothPorts = (): Promise<void> => {
	return dualPortManager.disconnectBothPorts();
};

export const isButtonsConnected = (): boolean => {
	return dualPortManager.isButtonsConnected();
};

export const isLedsConnected = (): boolean => {
	return dualPortManager.isLedsConnected();
};

export const areBothConnected = (): boolean => {
	return dualPortManager.areBothConnected();
};

// Legacy API Functions (for backward compatibility)
export const connectToSerialPort = async (): Promise<boolean> => {
	// For backward compatibility, try auto-connect first, then smart connect
	const result = await dualPortManager.connectToBothPorts();
	return result.buttons;
};

export const writeToSerialPort = async (data: string): Promise<boolean> => {
	// For backward compatibility, write to buttons port as default
	return await dualPortManager.writeToButtons(data);
};

export const disconnectSerialPort = async (): Promise<boolean> => {
	await dualPortManager.disconnectBothPorts();
	return true;
};

export const getPortState = (): SerialPortState => {
	// Return buttons port state for backward compatibility
	return {
		isConnected: dualPortManager.isButtonsConnected(),
		isConnecting: false,
		isReading: false,
		isWriting: false,
		error: null,
		lastActivity: null
	};
};

export const getPortInfo = (): Partial<SerialPortInfo> | null => {
	// Return null for backward compatibility
	return null;
};

export const isPortReady = (): boolean => {
	return dualPortManager.isButtonsConnected();
};

export const forceReconnect = async (): Promise<boolean> => {
	await dualPortManager.disconnectBothPorts();
	const result = await dualPortManager.connectToBothPorts();
	return result.buttons;
};

// Export stores for external use
export { portState };

// Cleanup on page unload
if (typeof window !== 'undefined') {
	// Handle visibility change (tab switching)
	document.addEventListener('visibilitychange', () => {
		if (document.visibilityState === 'hidden') {
			// Optionally pause operations when tab is hidden
			console.log('Tab hidden, serial port operations continue...');
		} else {
			// Optionally resume or check connection when tab becomes visible
			console.log('Tab visible, checking serial port connection...');
			if (dualPortManager.areBothConnected()) {
				// Optionally verify connection is still active
			}
		}
	});
}
