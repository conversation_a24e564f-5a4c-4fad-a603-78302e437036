import { dev } from '$app/environment';
import { writable } from 'svelte/store';

// Enhanced types for better error handling
export interface SerialPortConfig {
	baudRate: number;
	dataBits?: 7 | 8;
	stopBits?: 1 | 2;
	parity?: 'none' | 'even' | 'odd';
	bufferSize?: number;
	flowControl?: 'none' | 'hardware';
}

export interface SerialPortState {
	isConnected: boolean;
	isConnecting: boolean;
	isReading: boolean;
	isWriting: boolean;
	error: string | null;
	lastActivity: Date | null;
}

export interface SerialPortError {
	type:
		| 'connection'
		| 'permission'
		| 'timeout'
		| 'data'
		| 'browser_support'
		| 'port_busy'
		| 'unknown';
	message: string;
	originalError?: Error;
	timestamp: Date;
}

// Multi-port support types
export interface SerialPortConnection {
	id: string;
	port: SerialPort;
	config: SerialPortConfig;
	state: SerialPortState;
	data: SerialData;
	error: SerialPortError | null;
	reader: ReadableStreamDefaultReader<Uint8Array> | null;
	writer: WritableStreamDefaultWriter<Uint8Array> | null;
	reconnectAttempts: number;
	isCleaningUp: boolean;
}

export interface MultiPortState {
	connections: Map<string, SerialPortConnection>;
	defaultPortId: string | null;
}

// Default configuration
const DEFAULT_CONFIG: SerialPortConfig = {
	baudRate: 9600,
	dataBits: 8,
	stopBits: 1,
	parity: 'none',
	bufferSize: 255,
	flowControl: 'none'
};

// Timeout constants
const TIMEOUTS = {
	CONNECTION: 10000, // 10 seconds
	READ: 5000, // 5 seconds
	WRITE: 3000, // 3 seconds
	RECONNECT_DELAY: 2000 // 2 seconds
} as const;

// Store definitions
const portState = writable<SerialPortState>({
	isConnected: false,
	isConnecting: false,
	isReading: false,
	isWriting: false,
	error: null,
	lastActivity: null
});

export type SerialValue = '0' | '1' | '2';
export type SerialData = [
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue,
	SerialValue
];

// Legacy stores for backward compatibility
export const serialPortData = writable<SerialData>(['0', '0', '0', '0', '0', '0']);
export const serialPortError = writable<SerialPortError | null>(null);

// New multi-port stores
export const multiPortState = writable<MultiPortState>({
	connections: new Map(),
	defaultPortId: null
});
export const portDataMap = writable<Map<string, SerialData>>(new Map());
export const portErrorMap = writable<Map<string, SerialPortError | null>>(new Map());

// Constants
const maxReconnectAttempts = 3;

// Utility functions
const isWebSerialSupported = (): boolean => {
	return typeof navigator !== 'undefined' && 'serial' in navigator;
};

const createError = (
	type: SerialPortError['type'],
	message: string,
	originalError?: Error
): SerialPortError => {
	return {
		type,
		message,
		originalError,
		timestamp: new Date()
	};
};

// Multi-port Serial Port Manager
class MultiSerialPortManager {
	private connections = new Map<string, SerialPortConnection>();
	private defaultPortId: string | null = null;

	constructor() {
		// Initialize stores
		this.updateStores();
	}

	// Create a new connection object
	private createConnection(id: string, port: SerialPort, config: SerialPortConfig): SerialPortConnection {
		return {
			id,
			port,
			config,
			state: {
				isConnected: false,
				isConnecting: false,
				isReading: false,
				isWriting: false,
				error: null,
				lastActivity: null
			},
			data: ['0', '0', '0', '0', '0', '0'],
			error: null,
			reader: null,
			writer: null,
			reconnectAttempts: 0,
			isCleaningUp: false
		};
	}

	// Update all stores with current state
	private updateStores() {
		const state: MultiPortState = {
			connections: new Map(this.connections),
			defaultPortId: this.defaultPortId
		};
		multiPortState.set(state);

		// Update port data map
		const dataMap = new Map<string, SerialData>();
		const errorMap = new Map<string, SerialPortError | null>();

		for (const [id, connection] of this.connections) {
			dataMap.set(id, connection.data);
			errorMap.set(id, connection.error);
		}

		portDataMap.set(dataMap);
		portErrorMap.set(errorMap);

		// Update legacy stores with default port data
		if (this.defaultPortId && this.connections.has(this.defaultPortId)) {
			const defaultConnection = this.connections.get(this.defaultPortId)!;
			serialPortData.set(defaultConnection.data);
			serialPortError.set(defaultConnection.error);
			portState.set(defaultConnection.state);
		}
	}

	// Update connection state and refresh stores
	private updateConnectionState(id: string, updates: Partial<SerialPortState>) {
		const connection = this.connections.get(id);
		if (connection) {
			connection.state = { ...connection.state, ...updates };
			this.updateStores();
		}
	}

	// Set error for a specific connection
	private setConnectionError(id: string, error: SerialPortError) {
		const connection = this.connections.get(id);
		if (connection) {
			connection.error = error;
			connection.state.error = error.message;
			this.updateStores();
			console.error(`Serial Port Error [${id}][${error.type}]:`, error.message, error.originalError);
		}
	}

	// Clear error for a specific connection
	private clearConnectionError(id: string) {
		const connection = this.connections.get(id);
		if (connection) {
			connection.error = null;
			connection.state.error = null;
			this.updateStores();
		}
	}

	// Timeout wrapper for promises
	private withTimeout<T>(promise: Promise<T>, timeoutMs: number, errorMessage: string): Promise<T> {
		return Promise.race([
			promise,
			new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
			})
		]);
	}

	// Validation functions
	private validateData(data: string): boolean {
		if (typeof data !== 'string') return false;
		if (data.length === 0) return false;
		if (data.length > 1024) return false; // Reasonable size limit
		return true;
	}

	private isPortDisconnected(error: Error): boolean {
		const message = error.message.toLowerCase();
		return (
			message.includes('disconnected') ||
			message.includes('device not found') ||
			message.includes('port not open') ||
			message.includes('network error')
		);
	}

	// Connect to a specific port
	async connectPort(id: string, config: Partial<SerialPortConfig> = {}): Promise<boolean> {
		if (dev) {
			// In development mode, create a mock connection
			const mockConnection = this.createConnection(id, {} as SerialPort, { ...DEFAULT_CONFIG, ...config });
			mockConnection.state.isConnected = true;
			this.connections.set(id, mockConnection);
			if (!this.defaultPortId) {
				this.defaultPortId = id;
			}
			this.updateStores();
			return true;
		}

		// Check browser support
		if (!isWebSerialSupported()) {
			this.setConnectionError(id, createError('browser_support', 'Web Serial API is not supported in this browser'));
			return false;
		}

		// Check if already connecting
		const existingConnection = this.connections.get(id);
		if (existingConnection?.state.isConnecting) {
			console.warn(`Connection attempt already in progress for port ${id}`);
			return false;
		}

		if (existingConnection?.state.isConnected) {
			console.log(`Already connected to serial port ${id}`);
			return true;
		}

		// Create or update connection
		let connection = existingConnection;
		if (!connection) {
			// We'll create the connection after we get the port
		}

		try {
			const finalConfig = { ...DEFAULT_CONFIG, ...config };

			// Get available ports with timeout
			const ports = await this.withTimeout(
				navigator.serial.getPorts(),
				TIMEOUTS.CONNECTION,
				'Timeout while getting serial ports'
			);

			let selectedPort: SerialPort;

			if (ports.length === 0) {
				// Request port from user with timeout
				try {
					selectedPort = await this.withTimeout(
						navigator.serial.requestPort(),
						TIMEOUTS.CONNECTION,
						'Timeout while requesting serial port'
					);
				} catch (error) {
					if (error instanceof Error) {
						if (error.name === 'NotAllowedError') {
							this.setConnectionError(id, createError('permission', 'User denied access to serial port'));
						} else if (error.name === 'AbortError') {
							this.setConnectionError(id, createError('permission', 'User cancelled port selection'));
						} else {
							this.setConnectionError(id, createError('connection', 'Failed to request serial port', error));
						}
					}
					return false;
				}
			} else {
				// For now, use the first available port. In the future, we could implement port selection logic
				selectedPort = ports[0];
			}

			// Create connection if it doesn't exist
			if (!connection) {
				connection = this.createConnection(id, selectedPort, finalConfig);
				this.connections.set(id, connection);
			} else {
				connection.port = selectedPort;
				connection.config = finalConfig;
			}

			// Set as default port if none exists
			if (!this.defaultPortId) {
				this.defaultPortId = id;
			}

			this.updateConnectionState(id, { isConnecting: true });
			this.clearConnectionError(id);

			// Check if port is already open
			if (selectedPort.readable && selectedPort.writable) {
				console.log(`Port ${id} is already open, using existing connection`);
			} else {
				// Open port with timeout and error handling
				try {
					await this.withTimeout(
						selectedPort.open(finalConfig),
						TIMEOUTS.CONNECTION,
						'Timeout while opening serial port'
					);
				} catch (error) {
					if (error instanceof Error) {
						if (error.name === 'InvalidStateError') {
							this.setConnectionError(id, createError('port_busy', 'Serial port is already in use by another application'));
						} else if (error.name === 'NetworkError') {
							this.setConnectionError(id, createError('connection', 'Failed to open serial port - device may be disconnected', error));
						} else {
							this.setConnectionError(id, createError('connection', 'Failed to open serial port', error));
						}
					}
					return false;
				}
			}

			// Verify port is properly opened
			if (!selectedPort.readable || !selectedPort.writable) {
				this.setConnectionError(id, createError('connection', 'Port opened but streams are not available'));
				return false;
			}

			console.log(`Successfully connected to serial port ${id}: ${JSON.stringify(selectedPort.getInfo())}`);

			this.updateConnectionState(id, {
				isConnected: true,
				isConnecting: false,
				lastActivity: new Date()
			});

			// Start listening for data
			this.startListening(id);

			connection.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
			return true;
		} catch (error) {
			const serialError = error instanceof Error ? error : new Error('Unknown connection error');
			this.setConnectionError(id, createError('connection', 'Failed to connect to serial port', serialError));
			return false;
		} finally {
			this.updateConnectionState(id, { isConnecting: false });
		}
	}

	// Write data to a specific port
	async writeToPort(id: string, data: string): Promise<boolean> {
		// Validate input data
		if (!this.validateData(data)) {
			this.setConnectionError(id, createError('data', 'Invalid data: must be a non-empty string under 1024 characters'));
			return false;
		}

		const connection = this.connections.get(id);
		if (!connection) {
			console.log(`Port ${id} not found, attempting to connect...`);
			const connected = await this.connectPort(id);
			if (!connected) {
				return false;
			}
		}

		const currentConnection = this.connections.get(id)!;

		// Check if already writing
		if (currentConnection.state.isWriting) {
			console.warn(`Write operation already in progress for port ${id}`);
			return false;
		}

		// Check connection status
		if (!currentConnection.state.isConnected) {
			console.log(`Port ${id} not connected, attempting to connect...`);
			const connected = await this.connectPort(id);
			if (!connected) {
				return false;
			}
		}

		const port = currentConnection.port;
		if (!port || !port.writable) {
			this.setConnectionError(id, createError('connection', 'Serial port is not writable'));
			return false;
		}

		this.updateConnectionState(id, { isWriting: true });
		this.clearConnectionError(id);

		try {
			// Get writer with timeout
			let writer: WritableStreamDefaultWriter<Uint8Array>;
			try {
				writer = port.writable.getWriter();
				currentConnection.writer = writer;
			} catch (error) {
				console.warn(`Failed to get writer for port ${id}:`, error);
				this.setConnectionError(id, createError('port_busy', 'Failed to get writer - port may be busy'));
				return false;
			}

			console.log(`Writing data to serial port ${id}:`, data);

			// Encode and write data with timeout
			const encodedData = new TextEncoder().encode(data);

			try {
				await this.withTimeout(
					writer.write(encodedData),
					TIMEOUTS.WRITE,
					'Timeout while writing to serial port'
				);
			} catch (error) {
				if (error instanceof Error && this.isPortDisconnected(error)) {
					this.setConnectionError(id, createError('connection', 'Device disconnected during write operation', error));
					await this.cleanupConnection(id);
				} else {
					this.setConnectionError(id, createError('data', 'Failed to write data to serial port', error instanceof Error ? error : new Error('Unknown write error')));
				}
				return false;
			} finally {
				// Always release the writer
				try {
					writer.releaseLock();
					currentConnection.writer = null;
				} catch (e) {
					console.warn(`Error releasing writer lock for port ${id}:`, e);
				}
			}

			this.updateConnectionState(id, { lastActivity: new Date() });
			console.log(`Successfully wrote data to serial port ${id}`);
			return true;
		} catch (error) {
			const serialError = error instanceof Error ? error : new Error('Unknown write error');
			this.setConnectionError(id, createError('data', 'Failed to write to serial port', serialError));
			return false;
		} finally {
			this.updateConnectionState(id, { isWriting: false });
		}
	}

	// Disconnect a specific port
	async disconnectPort(id: string): Promise<boolean> {
		try {
			await this.cleanupConnection(id);
			this.connections.delete(id);

			// If this was the default port, clear it
			if (this.defaultPortId === id) {
				this.defaultPortId = null;
				// Set a new default if other connections exist
				if (this.connections.size > 0) {
					this.defaultPortId = this.connections.keys().next().value || null;
				}
			}

			this.updateStores();
			console.log(`Successfully disconnected from serial port ${id}`);
			return true;
		} catch (error) {
			const serialError = error instanceof Error ? error : new Error('Unknown disconnect error');
			this.setConnectionError(id, createError('connection', 'Error during disconnect', serialError));
			return false;
		}
	}

	// Cleanup a specific connection
	private async cleanupConnection(id: string): Promise<void> {
		const connection = this.connections.get(id);
		if (!connection || connection.isCleaningUp) return;

		connection.isCleaningUp = true;

		try {
			// Release reader
			if (connection.reader) {
				try {
					await connection.reader.cancel();
					connection.reader.releaseLock();
				} catch (e) {
					console.warn(`Error releasing reader for port ${id}:`, e);
				}
				connection.reader = null;
			}

			// Release writer
			if (connection.writer) {
				try {
					await connection.writer.close();
					connection.writer.releaseLock();
				} catch (e) {
					console.warn(`Error releasing writer for port ${id}:`, e);
				}
				connection.writer = null;
			}

			// Close port
			if (connection.port && connection.port.readable && connection.port.writable) {
				try {
					await connection.port.close();
				} catch (e) {
					console.warn(`Error closing port ${id}:`, e);
				}
			}

			this.updateConnectionState(id, {
				isConnected: false,
				isConnecting: false,
				isReading: false,
				isWriting: false
			});
		} finally {
			connection.isCleaningUp = false;
		}
	}

	// Start listening for data on a specific port
	private async startListening(id: string): Promise<void> {
		const connection = this.connections.get(id);
		if (!connection) return;

		// Prevent multiple simultaneous read operations
		if (connection.state.isReading) {
			console.warn(`Already listening for data on port ${id}`);
			return;
		}

		this.updateConnectionState(id, { isReading: true });

		let receivedData = '';
		let reader: ReadableStreamDefaultReader<Uint8Array> | null = null;

		try {
			// Ensure port is open and readable
			if (!connection.port.readable) {
				console.log(`Port ${id} not readable, attempting to open...`);
				try {
					await this.withTimeout(
						connection.port.open(connection.config),
						TIMEOUTS.CONNECTION,
						'Timeout while opening port for reading'
					);
				} catch (error) {
					this.setConnectionError(id, createError('connection', 'Failed to open port for reading', error instanceof Error ? error : new Error('Unknown open error')));
					return;
				}
			}

			// Verify port is still readable after opening
			if (!connection.port.readable) {
				this.setConnectionError(id, createError('connection', 'Port opened but readable stream is not available'));
				return;
			}

			// Get reader with error handling
			try {
				reader = connection.port.readable.getReader();
				connection.reader = reader;
			} catch (error) {
				console.warn(`Failed to get reader for port ${id}:`, error);
				this.setConnectionError(id, createError('port_busy', 'Failed to get reader - port may be busy'));
				return;
			}

			console.log(`Started listening for serial port data on ${id}`);

			// Main reading loop with comprehensive error handling
			while (true) {
				try {
					// Read continuously without timeout - always listen to COM port
					const result = await reader.read();
					const { value, done } = result;

					if (done) {
						console.log(`Serial port reading completed (stream closed) for ${id}`);
						break;
					}

					if (!value || value.length === 0) {
						continue; // Skip empty reads
					}

					// Decode received data
					const decoder = new TextDecoder('utf-8', { fatal: false });
					const chunk = decoder.decode(value, { stream: true });

					receivedData += chunk;
					this.updateConnectionState(id, { lastActivity: new Date() });

					// Process complete lines
					const lines = receivedData.split('\n');
					receivedData = lines.pop() || ''; // Keep incomplete line

					// Process each complete line
					for (const line of lines) {
						const trimmedLine = line.trim();
						if (trimmedLine.length === 0) continue;

						// Validate and process the data
						try {
							if (
								trimmedLine.split(',').length !== 6 ||
								trimmedLine.split(',').some((v) => !['0', '1', '2'].includes(v))
							) {
								console.warn(`Invalid data format from port ${id}:`, trimmedLine);
								connection.data = ['0', '0', '0', '0', '0', '0'];
								this.updateStores();
								continue;
							}
							connection.data = trimmedLine.split(',') as SerialData;
							this.updateStores();
							console.log(`Received serial data from ${id}:`, trimmedLine);
						} catch (parseError) {
							console.warn(`Failed to parse serial data from port ${id}:`, trimmedLine, parseError);
							connection.data = ['0', '0', '0', '0', '0', '0'];
							this.updateStores();
						}
					}
				} catch (readError) {
					if (readError instanceof Error) {
						// Handle different types of read errors
						if (this.isPortDisconnected(readError)) {
							console.log(`Device disconnected on port ${id}, attempting reconnection...`);
							this.setConnectionError(id, createError('connection', 'Device disconnected during read operation', readError));

							// Attempt reconnection if within limits
							if (connection.reconnectAttempts < maxReconnectAttempts) {
								connection.reconnectAttempts++;
								console.log(`Reconnection attempt ${connection.reconnectAttempts}/${maxReconnectAttempts} for port ${id}`);

								await this.cleanupConnection(id);
								await new Promise((resolve) => setTimeout(resolve, TIMEOUTS.RECONNECT_DELAY));

								// Try to reconnect
								const reconnected = await this.connectPort(id);
								if (reconnected) {
									console.log(`Reconnection successful for port ${id}`);
									return; // New startListening will be started by connectPort
								}
							} else {
								this.setConnectionError(id, createError('connection', `Failed to reconnect after ${maxReconnectAttempts} attempts`));
							}
							break;
						} else {
							this.setConnectionError(id, createError('data', 'Error reading from serial port', readError));
							break;
						}
					} else {
						this.setConnectionError(id, createError('unknown', 'Unknown error during read operation'));
						break;
					}
				}
			}
		} catch (error) {
			const serialError = error instanceof Error ? error : new Error('Unknown listen error');
			this.setConnectionError(id, createError('data', 'Failed to listen for serial port data', serialError));
		} finally {
			// Cleanup reader
			if (reader) {
				try {
					await reader.cancel();
					reader.releaseLock();
					connection.reader = null;
				} catch (e) {
					console.warn(`Error cleaning up reader for port ${id}:`, e);
				}
			}

			this.updateConnectionState(id, { isReading: false });
			console.log(`Stopped listening for serial port data on ${id}`);
		}
	}

	// Public API methods
	getPortState(id: string): SerialPortState | null {
		const connection = this.connections.get(id);
		return connection ? connection.state : null;
	}

	getPortData(id: string): SerialData | null {
		const connection = this.connections.get(id);
		return connection ? connection.data : null;
	}

	getPortInfo(id: string): Partial<SerialPortInfo> | null {
		const connection = this.connections.get(id);
		if (!connection || !connection.port) return null;

		try {
			return connection.port.getInfo();
		} catch (error) {
			console.warn(`Failed to get port info for ${id}:`, error);
			return null;
		}
	}

	isPortReady(id: string): boolean {
		const connection = this.connections.get(id);
		if (!connection) return false;

		return (
			connection.state.isConnected &&
			!connection.state.isConnecting &&
			connection.port !== null &&
			connection.port.readable !== null &&
			connection.port.writable !== null
		);
	}

	listConnectedPorts(): string[] {
		return Array.from(this.connections.keys()).filter(id =>
			this.connections.get(id)?.state.isConnected
		);
	}

	setDefaultPort(id: string): boolean {
		if (this.connections.has(id)) {
			this.defaultPortId = id;
			this.updateStores();
			return true;
		}
		return false;
	}

	getDefaultPortId(): string | null {
		return this.defaultPortId;
	}

	async forceReconnectPort(id: string): Promise<boolean> {
		console.log(`Forcing reconnection for port ${id}...`);
		const connection = this.connections.get(id);
		if (connection) {
			connection.reconnectAttempts = 0; // Reset reconnect attempts
			await this.cleanupConnection(id);
		}
		return await this.connectPort(id);
	}

	async disconnectAllPorts(): Promise<boolean> {
		const results = await Promise.all(
			Array.from(this.connections.keys()).map(id => this.disconnectPort(id))
		);
		return results.every(result => result);
	}
}

// Create global manager instance
const serialPortManager = new MultiSerialPortManager();

// New Multi-Port API Functions
export const connectToPort = (id: string, config?: Partial<SerialPortConfig>): Promise<boolean> => {
	return serialPortManager.connectPort(id, config);
};

export const disconnectPort = (id: string): Promise<boolean> => {
	return serialPortManager.disconnectPort(id);
};

export const writeToPort = (id: string, data: string): Promise<boolean> => {
	return serialPortManager.writeToPort(id, data);
};

export const getPortDataById = (id: string): SerialData | null => {
	return serialPortManager.getPortData(id);
};

export const getPortStateById = (id: string): SerialPortState | null => {
	return serialPortManager.getPortState(id);
};

export const getPortInfoById = (id: string): Partial<SerialPortInfo> | null => {
	return serialPortManager.getPortInfo(id);
};

export const isPortReadyById = (id: string): boolean => {
	return serialPortManager.isPortReady(id);
};

export const listConnectedPorts = (): string[] => {
	return serialPortManager.listConnectedPorts();
};

export const setDefaultPort = (id: string): boolean => {
	return serialPortManager.setDefaultPort(id);
};

export const getDefaultPortId = (): string | null => {
	return serialPortManager.getDefaultPortId();
};

export const forceReconnectPort = (id: string): Promise<boolean> => {
	return serialPortManager.forceReconnectPort(id);
};

export const disconnectAllPorts = (): Promise<boolean> => {
	return serialPortManager.disconnectAllPorts();
};

// Legacy API Functions (for backward compatibility)
export const connectToSerialPort = async (config: Partial<SerialPortConfig> = {}): Promise<boolean> => {
	return await serialPortManager.connectPort('default', config);
};

export const writeToSerialPort = async (data: string): Promise<boolean> => {
	const defaultId = serialPortManager.getDefaultPortId() || 'default';
	return await serialPortManager.writeToPort(defaultId, data);
};

export const disconnectSerialPort = async (): Promise<boolean> => {
	const defaultId = serialPortManager.getDefaultPortId() || 'default';
	return await serialPortManager.disconnectPort(defaultId);
};

export const getPortState = (): SerialPortState => {
	const defaultId = serialPortManager.getDefaultPortId();
	if (defaultId) {
		const state = serialPortManager.getPortState(defaultId);
		if (state) return state;
	}

	// Return default state if no default port
	return {
		isConnected: false,
		isConnecting: false,
		isReading: false,
		isWriting: false,
		error: null,
		lastActivity: null
	};
};

export const getPortInfo = (): Partial<SerialPortInfo> | null => {
	const defaultId = serialPortManager.getDefaultPortId();
	return defaultId ? serialPortManager.getPortInfo(defaultId) : null;
};

export const isPortReady = (): boolean => {
	const defaultId = serialPortManager.getDefaultPortId();
	return defaultId ? serialPortManager.isPortReady(defaultId) : false;
};

export const forceReconnect = async (): Promise<boolean> => {
	const defaultId = serialPortManager.getDefaultPortId() || 'default';
	return await serialPortManager.forceReconnectPort(defaultId);
};

// Export stores for external use
export { portState };

// Cleanup on page unload
if (typeof window !== 'undefined') {
	// Handle visibility change (tab switching)
	document.addEventListener('visibilitychange', () => {
		if (document.visibilityState === 'hidden') {
			// Optionally pause operations when tab is hidden
			console.log('Tab hidden, serial port operations continue...');
		} else {
			// Optionally resume or check connection when tab becomes visible
			console.log('Tab visible, checking serial port connection...');
			const defaultId = serialPortManager.getDefaultPortId();
			if (defaultId && serialPortManager.isPortReady(defaultId)) {
				// Optionally verify connection is still active
			}
		}
	});
}
