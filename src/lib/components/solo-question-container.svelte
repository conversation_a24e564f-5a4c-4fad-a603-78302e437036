<script lang="ts">
	import { goto } from '$app/navigation';
	import { cleanHtml, cn } from '$lib';
	import { logEvent } from '$lib/api';
	import { appStore, type Question } from '$lib/app.state';
	import { playAudio, stopAudio } from '$lib/audio';
	import SimplePngSequencePlayer from '$lib/components/SimplePngSequencePlayer.svelte';
	import { type Player } from '$lib/player.state';
	import { writeToSerialPort } from '$lib/serial-port';
	import { onMount } from 'svelte';
	import type { Writable } from 'svelte/store';
	import { fade, scale } from 'svelte/transition';

	type Props = {
		player: Writable<Player>;
		questionIndex: number;
		currentQuestion: Question;
		nextQuestion: () => boolean;
	};

	const { player, questionIndex, currentQuestion, nextQuestion }: Props = $props();

	const TOTAL_QUESTIONS = 5;
	const ANSWER_TIMEOUT_SECONDS = 8;
	const FACT_TIMEOUT_SECONDS = 11;

	let cdInterval = $state<NodeJS.Timeout | undefined>();
	let currentTime = $state(ANSWER_TIMEOUT_SECONDS);
	let revealedAnswer = $state(false);

	let showFact = $state(false);
	let factTimer = $state<NodeJS.Timeout | undefined>();
	let factCounter = $state(FACT_TIMEOUT_SECONDS);

	let shouldShowScoreAnimation = $state(false);
	let shouldShowBonusAnimation = $state(false);

	function setupCountdown() {
		writeToSerialPort('o');
		currentTime = ANSWER_TIMEOUT_SECONDS;
		playAudio('tickTock');

		cdInterval = setInterval(() => {
			currentTime--;
			if (currentTime === 0) {
				stopAudio('tickTock');
				revealedAnswer = true;
				clearInterval(cdInterval);

				$player.score = $player.results.reduce((acc, result) => {
					return acc + (result.isCorrect ? 20 : 0) + (result.isBonus ? 10 : 0);
				}, 0);

				logEvent(
					$appStore.interactionId,
					$player.results.at(questionIndex)
						? `[${$player.name}] answered "${cleanHtml(currentQuestion.question)}" with "${cleanHtml(currentQuestion.answers[$player.results.at(questionIndex)!.answer])}" in ${ANSWER_TIMEOUT_SECONDS - $player.results.at(questionIndex)!.currentTime!}s`
						: `[${$player.name}] answered "${cleanHtml(currentQuestion.question)}" with "No answer" in ${ANSWER_TIMEOUT_SECONDS}s`
				);

				if ($player.results.at(questionIndex)?.isCorrect) {
					playAudio('correct');
					shouldShowScoreAnimation = true;
					setTimeout(() => {
						shouldShowScoreAnimation = false;
					}, 1700);
				}

				if ($player.results.at(questionIndex)?.isBonus) {
					shouldShowBonusAnimation = true;
					setTimeout(() => {
						shouldShowBonusAnimation = false;
					}, 1700);
				}

				gotoNextQuestion();
			}
		}, 1000);

		return () => clearInterval(cdInterval);
	}

	function gotoNextQuestion() {
		factCounter = FACT_TIMEOUT_SECONDS;

		factTimer = setInterval(() => {
			if (factCounter === 10) {
				showFact = true;
			}

			if (factCounter === 0) {
				clearInterval(factTimer);

				if (nextQuestion()) {
					showFact = false;
					revealedAnswer = false;
					setupCountdown();
				} else {
					playAudio('gameOver');
					logEvent($appStore.interactionId, `[${$player.name}] game over`);
					goto('/timesup');
				}
			} else {
				factCounter--;
			}
		}, 1000);
	}

	function setupKeyListener() {
		$player.results = [];

		const unsub = player.subscribe(({ pressedKey }) => {
			if (pressedKey !== -1 && !showFact && !$player.results[questionIndex]) {
				if (currentTime === 0) return;

				const answered = $player.results.find((r) => r.questions === currentQuestion.question);

				const isCorrect = pressedKey === currentQuestion.correctAnswer;
				const isBonus =
					currentTime >= ANSWER_TIMEOUT_SECONDS - 2 && pressedKey === currentQuestion.correctAnswer;

				if (answered) {
					if (answered.answer === pressedKey) return;

					answered.answer = pressedKey;
					answered.isCorrect = isCorrect;
					answered.isBonus = isBonus;
					answered.currentTime = currentTime;
				} else {
					$player.results[questionIndex] = {
						questions: currentQuestion.question,
						answer: pressedKey,
						correctAnswer: currentQuestion.correctAnswer,
						isCorrect,
						isBonus,
						currentTime
					};

					revealedAnswer = true;
					clearInterval(cdInterval);

					$player.score = $player.results.reduce((acc, result) => {
						return acc + (result.isCorrect ? 20 : 0) + (result.isBonus ? 10 : 0);
					}, 0);

					logEvent(
						$appStore.interactionId,
						$player.results.at(questionIndex)
							? `[${$player.name}] answered "${cleanHtml(currentQuestion.question)}" with "${cleanHtml(currentQuestion.answers[$player.results.at(questionIndex)!.answer])}" in ${ANSWER_TIMEOUT_SECONDS - $player.results.at(questionIndex)!.currentTime!}s`
							: `[${$player.name}] answered "${cleanHtml(currentQuestion.question)}" with "No answer" in ${ANSWER_TIMEOUT_SECONDS}s`
					);

					if ($player.results.at(questionIndex)?.isCorrect) {
						playAudio('correct');
						shouldShowScoreAnimation = true;
						setTimeout(() => {
							shouldShowScoreAnimation = false;
						}, 1700);
					}

					if ($player.results.at(questionIndex)?.isBonus) {
						shouldShowBonusAnimation = true;
						setTimeout(() => {
							shouldShowBonusAnimation = false;
						}, 1700);
					}

					gotoNextQuestion();
				}
			}
		});

		return () => unsub();
	}

	onMount(() => {
		return setupCountdown();
	});

	onMount(() => {
		return setupKeyListener();
	});
</script>

<div class="flex justify-between px-27 pt-12">
	<div class="font-parkin relative w-fit leading-none font-bold text-white uppercase">
		<img src={`/assets/el/left-score.png`} alt="score" />

		<span class="absolute top-7 left-32.5 text-[49.67px] tracking-[0.062em]">
			{$player.name}
		</span>

		<div class="absolute top-16 left-19.5 h-33.5 w-px">
			<span
				class="font-raleway absolute top-1/2 left-1/2 -translate-1/2 text-[79.48px] leading-0 tracking-[0.062em]"
			>
				{$player.name[0]}
			</span>
		</div>

		<span
			class={cn('absolute top-23.5 left-48 w-31.5 text-center text-[77.9px]', {
				'blink text-red-500':
					$player.results.at(questionIndex)?.answer === undefined && currentTime === 0
			})}
		>
			{$player.score}
		</span>

		{#if shouldShowScoreAnimation}
			<img class="fade-up absolute top-48 left-40" src="/assets/el/20p.png" alt="20p" />
		{/if}
		{#if shouldShowBonusAnimation}
			<img class="fade-up absolute top-44 left-88" src="/assets/el/10p.png" alt="10p" />
		{/if}
	</div>
</div>

<div
	class="font-parkin absolute top-0 left-1/2 -translate-x-1/2 leading-none font-bold text-white uppercase"
>
	<div class="h-73 w-110"></div>
	<span class="absolute top-23 left-37.5 text-[112.79px]"> {questionIndex + 1} </span>
	<span class="absolute top-36 right-35.5 text-[70px]"> {TOTAL_QUESTIONS} </span>
</div>

<div class="relative flex size-full flex-col pt-16" in:fade>
	<div class="flex h-[280px] items-center justify-center">
		<p
			class="font-parkin text-center text-[54px] leading-[60px] font-extrabold tracking-wider text-white uppercase"
		>
			{@html currentQuestion.question}
		</p>
	</div>

	<div class="flex gap-x-5 px-55 pt-5.5">
		{#each currentQuestion.answers as answer, index}
			<div
				class="relative"
				class:opacity-50={index !== currentQuestion.correctAnswer && revealedAnswer}
			>
				<img
					width="480"
					src="/assets/el/{$player.results.at(questionIndex)?.answer === undefined &&
					currentTime === 0
						? 'wrong-answer-box'
						: 'answer-box'}.png"
					alt="answer box"
				/>

				{#if $player.results.at(questionIndex)?.answer === index}
					<div class="absolute top-0 left-1/2 -translate-x-1/2">
						<img src="/assets/el/profile-icon-yellow.png" alt="profile" />
						<span
							class="font-raleway absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[42.79px] leading-0 font-bold text-white uppercase"
						>
							{$player.name[0]}
						</span>
					</div>
				{/if}

				{#if revealedAnswer && index === currentQuestion.correctAnswer}
					<img
						in:scale={{ duration: 400 }}
						class="absolute -right-4 -bottom-3 z-50"
						src="/assets/el/tick.png"
						alt="indicator"
					/>
				{/if}

				<span class="font-parkin absolute top-14.5 left-11 text-[52px] font-bold text-white">
					{#if index === 0}
						A
					{:else if index === 1}
						B
					{:else}
						C
					{/if}
				</span>

				<div
					class="font-marqona absolute top-21 left-22.5 flex h-[126px] w-[311px] items-center justify-center text-center font-bold tracking-widest text-nowrap text-white"
				>
					{@html answer}
				</div>
			</div>
		{/each}
	</div>

	<div class="relative mt-auto">
		<img class="z-0 w-full object-cover" src="/assets/el/question-timer.png" alt="counter" />

		{#key questionIndex}
			<div
				class="timeIndicator absolute bottom-0 left-0 h-7.5 w-full rounded-full bg-gradient-to-r from-[#C13028] via-[#FD947C] to-[#EB4239]"
			></div>
		{/key}
		<img class="absolute bottom-0 left-0 z-2" src="/assets/el/clock.png" alt="clock" />
		<span
			class="font-parkin absolute top-8.5 left-28 text-[80px] font-extrabold tracking-wide text-white uppercase"
		>
			{currentTime.toString().padStart(2, '0')}<span class="text-[33.12px] font-bold">s</span>
		</span>
	</div>
</div>

{#if showFact}
	<div class="absolute top-0 left-0 z-50 flex size-full items-center justify-center text-white">
		<h1
			class="font-parkin absolute top-38 left-1/2 z-40 -translate-x-1/2 text-[70px] leading-none font-extrabold uppercase"
			in:fade
		>
			{#if questionIndex === 4}
				Game over IN...{factCounter}
			{:else}
				NEXT QUESTION IN...{factCounter}
			{/if}
		</h1>

		<div class="relative z-30" in:scale>
			<SimplePngSequencePlayer
				class="size-full"
				imageUrls={Array.from(
					{ length: 480 },
					(_, i) => `/assets/pngSequences/did-you-know/${String(i).padStart(5, '0')}.webp`
				)}
				fps={60}
				loop={true}
			/>

			<div class="absolute top-115 right-110 flex h-55 w-160 items-center">
				<p class="font-phluff text-left text-[31.5px] leading-[40px] font-medium">
					{currentQuestion.fact}
				</p>
			</div>
		</div>
	</div>
{/if}

<style>
	.blink {
		animation: blink 0.8s ease-in infinite;
	}

	@keyframes blink {
		0% {
			opacity: 1;
		}
		50% {
			opacity: 0;
		}
	}

	.timeIndicator {
		animation: timeIndicator 8s linear forwards;
	}

	@keyframes timeIndicator {
		from {
			width: 100%;
		}
		to {
			width: 0%;
		}
	}

	.fade-up {
		animation: fade-up 1.5s ease-in-out forwards;
	}

	@keyframes fade-up {
		0% {
			transform: translateY(0);
			opacity: 1;
		}
		100% {
			transform: translateY(-80px);
			opacity: 0;
		}
	}
</style>
