import { player1, player2 } from '$lib/player.state';
import type { SerialData } from '$lib/serial-port';
import { get } from 'svelte/store';

export function handleKeydown(event: KeyboardEvent) {
	if (['1', '2', '3', '4', '5', '6'].includes(event.key)) {
		if (+event.key <= 3) {
			if (get(player1).pressedKey !== -1) return;
			player1.update((state) => {
				state.pressedKey = +event.key - 1;
				return state;
			});
		} else {
			if (get(player2).pressedKey !== -1) return;
			player2.update((state) => {
				state.pressedKey = +event.key - 4;
				return state;
			});
		}
	}
}

export function handleKeyup(event: KeyboardEvent) {
	if (['1', '2', '3', '4', '5', '6'].includes(event.key)) {
		if (+event.key <= 3) {
			player1.update((state) => {
				state.pressedKey = -1;
				return state;
			});
		} else {
			player2.update((state) => {
				state.pressedKey = -1;
				return state;
			});
		}
	}
}

export function handleArduinoResponse(data: SerialData) {
	if (data[0] || data[1] || data[2]) {
		player1.update((state) => {
			if (data[0]) state.pressedKey = -1;
			else if (data[1]) state.pressedKey = 1;
			else if (data[2]) state.pressedKey = 2;
			else state.pressedKey = -1;
			return state;
		});
	}

	if (data[3] || data[4] || data[5]) {
		player2.update((state) => {
			if (data[3]) state.pressedKey = -1;
			else if (data[4]) state.pressedKey = 1;
			else if (data[5]) state.pressedKey = 2;
			else state.pressedKey = -1;
			return state;
		});
	}
}
